import 'dart:developer';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/material.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/services.dart';
import 'package:newarc_platform/app_config.dart' as appConfig;
import 'package:newarc_platform/app_const.dart' as appConst;
import 'package:newarc_platform/classes/renovationQuotation.dart';
import 'package:newarc_platform/utils/color_schema.dart';
import 'package:newarc_platform/utils/downloadQuotationPDF.dart';
import 'package:newarc_platform/utils/various.dart';
import 'package:newarc_platform/widget/UI/base_newarc_button.dart';
import 'package:newarc_platform/widget/UI/base_newarc_popup.dart';
import 'package:newarc_platform/widget/UI/custom_textformfield.dart';
import 'package:newarc_platform/widget/UI/form-label.dart';
import 'package:newarc_platform/widget/UI/link.dart';
import 'package:newarc_platform/widget/UI/multi-select-dropdown.dart';
import 'package:newarc_platform/widget/UI/select-box.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:intl/intl.dart';
import 'package:newarc_platform/activitiesForEstimate.dart' as aef;
import 'package:newarc_platform/widget/UI/tab/icon_text_button.dart';
import '../../classes/NewarcProjectFixedAssetsPropertyCategory.dart';
import '../../classes/newarcProjectFixedAssetsPropertyPagamento.dart';
import '../../classes/newarcProjectFixedAssetsPropertyPercentage.dart';
import '../UI/checkbox.dart';
import '../UI/resizable_note_text_field.dart';
import '../UI/tab/common_icon_button.dart';


class RenovationQuotationSingle extends StatefulWidget {
  final RenovationQuotation? renovationQuotation;
  final Function? updateViewCallback;

  const RenovationQuotationSingle(
      {Key? key, this.renovationQuotation, this.updateViewCallback})
      : super(key: key);

  @override
  State<RenovationQuotationSingle> createState() =>
      _RenovationQuotationSingleState();
}

class _RenovationQuotationSingleState extends State<RenovationQuotationSingle> {
  RenovationQuotation? initRenovationQuotation;
  bool changedDetected = false;


  NumberFormat localCurrencyFormatMain = NumberFormat.currency(locale: 'it_IT', symbol: '', decimalDigits: 2);

  List<String> unitOfMeasurement = [
    'AC',
    'GI',
    'MQ',
    'ML',
    'LT',
    'NR',
    'MC',
    'T',
    'H',
    'KG',
    '%'
  ];

  List<String> priceLevel = ['F1', 'F2', 'F3', 'Manuale'];

  List<String> categories = [];
  List<String> subCategories = [];
  List activities = [];

  List<String> paymentMode = ['Da definire'];

  List<String> constructionDuration = [
    'Da definire',
    '30 giorni lavorativi',
    '45 giorni lavorativi',
    '60 giorni lavorativi',
    '75 giorni lavorativi',
    '90 giorni lavorativi'
  ];

  List<String> discountTypes = [
    'Percentuale',
    'Fisso'
  ];

  List<String> discounts = [
    '0%',
    '5%',
    '10%',
    '15%',
    '20%'
  ];

  bool addManualActivity = false;

  TextEditingController? contNotes = new TextEditingController();
  TextEditingController? contCategory = new TextEditingController();
  TextEditingController? contSubCategory = new TextEditingController();
  TextEditingController? contActivity = new TextEditingController();
  TextEditingController? contManualActivity = new TextEditingController();
  TextEditingController? contUM = new TextEditingController();
  TextEditingController? contQuantity = new TextEditingController();
  TextEditingController? contUnitCost = new TextEditingController();
  TextEditingController? contTotal = new TextEditingController();
  TextEditingController? contDescription = new TextEditingController();
  TextEditingController? contPriceLevel = new TextEditingController();
  TextEditingController? contComment = new TextEditingController();

  TextEditingController? contConstructionDuration = new TextEditingController();
  // TextEditingController? contPaymentMode = new TextEditingController();

  TextEditingController? contDiscountType = new TextEditingController();
  TextEditingController? contDiscount = new TextEditingController();

  Map<String, Map<String, List<Map>>> test = {};


  Map<String, Map<String, List<Map>>> renovationData = {};
  Map<String, Map<String, List<Map>>> initialRenovationData = {};
  List<String> categoryOrder = [];
  Map<String, TextEditingController> categoryControllers = {};

  Map categoryTotal = {};
  Map categoryTotalForOnlyToShow = {};
  Map<String, Map> subCategoryTotal = {};
  Map<String, Map> subCategoryTotalForOnlyToShow = {};
  List<String> formMessages = [''];
  GlobalKey<FormState> _formKey = new GlobalKey<FormState>();

  Map<String, bool> expandedCategories = {};

  List selectedActivities = [];

  double grandTotal = 0;

  bool isLoadingPagamenti = false;
  bool isReCalculatingPagamenti = false;
  bool isCreatingPagamenti = false;

  void toggleCategory(String category) {
    setState(() {
      expandedCategories[category] = !(expandedCategories[category] ?? false);
    });
  }

  @override
  void initState() {
    super.initState();
    paymentMode.addAll(appConst.installmentList
        .map((item) => item.replaceAll(' - ', '-'))
        .toList());

    categories.addAll(aef.activitiesForEstimate.keys);

    initRenovationQuotation = widget.renovationQuotation;

    contConstructionDuration!.text =
    widget.renovationQuotation!.constructionDuration != null
        ? widget.renovationQuotation!.constructionDuration!
        : '';
    // contPaymentMode!.text = widget.renovationQuotation!.paymentMode != null
    //     ? widget.renovationQuotation!.paymentMode!
    //     : '';

    contDiscountType!.text = widget.renovationQuotation!.discountType == null ? 'Percentuale' :
    widget.renovationQuotation!.discountType == "" ? 'Percentuale' :
    widget.renovationQuotation!.discountType == 'Percentage' ? 'Percentuale' :
    widget.renovationQuotation!.discountType == 'Percentuale' ? 'Percentuale' : 'Fisso';


    contDiscount!.text = widget.renovationQuotation!.discountType == "Fisso" ? (widget.renovationQuotation?.discount?.isNotEmpty ?? false ? localCurrencyFormatMain.format(double.tryParse(widget.renovationQuotation?.discount ?? "")) : "") : widget.renovationQuotation?.discount ?? '0%';


    if (widget.renovationQuotation!.renovationActivity != null) {
      for (var i = 0;
      i < widget.renovationQuotation!.renovationActivity!.length;
      i++) {
        RenovationActivityCategory tmpRenovationActivityCategory =
        widget.renovationQuotation!.renovationActivity![i];

        if (!renovationData.containsKey(tmpRenovationActivityCategory.category)) {

          renovationData[tmpRenovationActivityCategory.category!] = {};

        }

        for (var j = 0; j < tmpRenovationActivityCategory.activity!.length; j++) {

          Map rowData = {
            'index': tmpRenovationActivityCategory.activity![j].index,
            'title': tmpRenovationActivityCategory.activity![j].title,
            'measurementUnit':
            tmpRenovationActivityCategory.activity![j].measurementUnit,
            'quantity': tmpRenovationActivityCategory.activity![j].quantity,
            'unitPrice': localCurrencyFormatMain.format(tmpRenovationActivityCategory.activity![j].unitPrice ?? 0.0),
            'description':
            tmpRenovationActivityCategory.activity![j].description,
            'priceLevel': tmpRenovationActivityCategory.activity![j].priceLevel,
            'subCategory':
            tmpRenovationActivityCategory.activity![j].subCategory,
            'code': tmpRenovationActivityCategory.activity![j].code,
            'comment': tmpRenovationActivityCategory.activity![j].comment,
            'isDiscounted': tmpRenovationActivityCategory.activity![j].isDiscounted,
            'isManualActivity': tmpRenovationActivityCategory.activity![j].isManualActivity,
            'isIncludedInComputoMatric': tmpRenovationActivityCategory.activity![j].isIncludedInComputoMatric,
            'activityDiscountAmount': tmpRenovationActivityCategory.activity![j].activityDiscountAmount,
            'activityDiscount': tmpRenovationActivityCategory.activity![j].activityDiscount,
          };


          setDataRow(tmpRenovationActivityCategory.category!, rowData);
        }
      }
    }


    if( renovationData.containsKey('N - Newarc') ) {
      renovationData = {
        'N - Newarc': renovationData['N - Newarc']!,
        ...renovationData..remove('N - Newarc'),
      };
    }


    contConstructionDuration!.addListener(_checkForChanges);
    // contPaymentMode!.addListener(_checkForChanges);
    contCategory!.addListener(_checkForChanges);
    contSubCategory!.addListener(_checkForChanges);
    contActivity!.addListener(_checkForChanges);
    contManualActivity!.addListener(_checkForChanges);
    contUM!.addListener(_checkForChanges);
    contQuantity!.addListener(_checkForChanges);
    contPriceLevel!.addListener(_checkForChanges);
    contUnitCost!.addListener(_checkForChanges);
    contTotal!.addListener(_checkForChanges);
    contDescription!.addListener(_checkForChanges);
    contDiscountType!.addListener(_checkForChanges);
    contDiscount!.addListener(_checkForChanges);

    initialRenovationData = renovationData;

    subCategoryTotalCalc();

    setState(() {});

    fetchPagamento();

  }

  @override
  void dispose() {
    contConstructionDuration!.removeListener(_checkForChanges);
    // contPaymentMode!.removeListener(_checkForChanges);
    contCategory!.removeListener(_checkForChanges);
    contSubCategory!.removeListener(_checkForChanges);
    contActivity!.removeListener(_checkForChanges);
    contManualActivity!.removeListener(_checkForChanges);
    contUM!.removeListener(_checkForChanges);
    contQuantity!.removeListener(_checkForChanges);
    contPriceLevel!.removeListener(_checkForChanges);
    contUnitCost!.removeListener(_checkForChanges);
    contTotal!.removeListener(_checkForChanges);
    contDescription!.removeListener(_checkForChanges);
    contDiscountType!.removeListener(_checkForChanges);
    contDiscount!.removeListener(_checkForChanges);

    super.dispose();
  }

  @protected
  void didUpdateWidget(RenovationQuotationSingle oldWidget) {
    super.didUpdateWidget(oldWidget);

    // setInitialValues();
  }

  setSubCategories() {
    subCategories.clear();
    activities.clear();
    String selectedCategory = contCategory!.text;
    subCategories.addAll(aef.activitiesForEstimate[selectedCategory]!.keys);
  }

  subCategoryTotalCalc(){

    subCategoryTotal.clear();
    subCategoryTotalForOnlyToShow.clear();
    categoryTotalForOnlyToShow.clear();
    categoryTotal.clear();
    grandTotal = 0;

    categoryOrder.clear();

    categoryOrder = renovationData.keys.toList();

    Map<String, bool> previousExpandedCategories = Map.from(expandedCategories);

    renovationData.keys.map((category){

      expandedCategories[category] = previousExpandedCategories[category] ?? true;

      categoryControllers[category] = TextEditingController();

      if (!categoryTotal.containsKey(category)) {
        categoryTotal[category] = 0;
      }
      double catTotal = 0;
      double catTotalForPdf = 0;
      bool isCategoryDiscounted = false;
      renovationData[category]!.keys.map((subCategory) {
          renovationData[ category]![subCategory]! .map((entry) {

              if( !subCategoryTotal.containsKey(category) ){
                subCategoryTotal[category] = {};
              }

              if (!subCategoryTotal[category]!.containsKey(subCategory)) {
                subCategoryTotal[category]![subCategory] = 0;
              }

              if (!subCategoryTotalForOnlyToShow.containsKey(category)) {
                subCategoryTotalForOnlyToShow[category] = {};
              }

              if (!subCategoryTotalForOnlyToShow[category]!.containsKey(subCategory)) {
                subCategoryTotalForOnlyToShow[category]![subCategory] = {
                  "subCategoryTotal": 0,
                  "isDiscounted": false,
                  "subCategoryTotalWithoutDiscount": 0,
                };
              }

              bool isSubCategoryDiscounted = false;

              if( !entry['isDiscounted'] ) {

                double qty = (double.tryParse( entry['quantity'].toString()) ?? 0.0);


                double price = (double.tryParse(entry['unitPrice'].toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0.0);

                double total = (qty * price);

                subCategoryTotal[category]![subCategory] += total;
                if (subCategoryTotalForOnlyToShow.containsKey(category)) {
                  subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += total;
                  subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotalWithoutDiscount"] += total;
                }
                catTotal += total;
                catTotalForPdf += total;
             }else{
                double activityDiscountAmount = (double.tryParse( entry['activityDiscountAmount'].toString()) ?? 0.0);

                if(![0.0,0].contains(activityDiscountAmount)){
                  isSubCategoryDiscounted = true;
                }

                double qty = (double.tryParse( entry['quantity'].toString()) ?? 0.0);
                double price = (double.tryParse(entry['unitPrice'].toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0.0);

                double total = (qty * price) - activityDiscountAmount;

                double totalWithoutDiscount = (qty * price);

                catTotal += total;
                catTotalForPdf += total;
                if (subCategoryTotalForOnlyToShow.containsKey(category)) {
                  subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += total;
                  subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotalWithoutDiscount"] += totalWithoutDiscount;
                }
                isSubCategoryDiscounted = true;
              }

              //  If any entry in this subcategory was discounted, mark the whole subcategory as discounted
              if (isSubCategoryDiscounted) {
                subCategoryTotalForOnlyToShow[category]![subCategory]!["isDiscounted"] = true;
                isCategoryDiscounted = true; // The entire category should also be marked discounted
              }
          }).toList();
      }).toList();

      categoryTotal[category] = catTotal;

      double catDiscountedAmount = catTotal;
      if(widget.renovationQuotation?.discount?.isNotEmpty ?? false){
        if(widget.renovationQuotation!.discount!.contains("%")){
          double wholeQuotationDiscountDouble = double.tryParse(widget.renovationQuotation!.discount!.replaceAll("%", "")) ?? 0.0;
          double catValueWithDiscount = catTotal * wholeQuotationDiscountDouble / 100;
          catDiscountedAmount = catTotal - catValueWithDiscount;
        }
      }
      grandTotal += catDiscountedAmount;

      categoryTotalForOnlyToShow[category] = {
        "catTotal":catTotal,
        "catTotalForPdf":catTotalForPdf,
        "isDiscounted": isCategoryDiscounted,
        "catDiscountedAmount": catDiscountedAmount,
      };

    }).toList();

    if(widget.renovationQuotation?.discount?.isNotEmpty ?? false){
      if(!widget.renovationQuotation!.discount!.contains("%")){
        double wholeQuotationDiscountDouble = double.tryParse(widget.renovationQuotation!.discount!) ?? 0.0;
        double catDiscountedAmountManual = grandTotal - wholeQuotationDiscountDouble;
        grandTotal = catDiscountedAmountManual;
      }
    }

  }

  setActivities() {
    activities.clear();
    String selectedSubCategory = contSubCategory!.text;
    String selectedCategory = contCategory!.text;

    activities.addAll(aef
        .activitiesForEstimate[selectedCategory]![selectedSubCategory]!
        .map((value) =>
            {'label': value['activity'], 'value': value['activity']}));

    dynamic act = activities.first;
    contActivity!.text = act['value'];
    Map activityData = aef
        .activitiesForEstimate[selectedCategory]![selectedSubCategory]!
        .firstWhere((e) => e['activity'] == activities.first, orElse: () => {'um': ''});

    contUM!.text = activityData['um'];

    setState(() {});
  }

  _checkForChanges() {
    changedDetected = true;
  }

  setDataRow(String category, Map data) {
    try {

      String subCategory = data['subCategory'];
      if (!renovationData.containsKey(category)) {
        renovationData[category] = {};
      }

      if (!renovationData[category]!.containsKey(subCategory)) {
        renovationData[category]![subCategory] = [];
      }

      int _index = -1;

      if (data.containsKey('index')) {
        _index = data['index'];
      } else {
        _index = renovationData[category]![subCategory]!.indexWhere((e) {
          return e['title'] == data['title'];
        });
      }


      double total = double.tryParse(data['quantity'].toString())! *
          double.tryParse(data['unitPrice'].toString().replaceAll(".", "").replaceAll(",", "."))!;


      data['index'] = renovationData[category]![subCategory]!.length;

      data['total'] = total;
      data['contUM'] = new TextEditingController();
      data['contQuantity'] = new TextEditingController();
      data['contPriceLevel'] = new TextEditingController();
      data['contUnitPrice'] = new TextEditingController();
      data['contTotal'] = new TextEditingController();
      data['contComment'] = new TextEditingController();


      data['contUM'].text = data['measurementUnit'].toString();
      data['contQuantity'].text = data['quantity'].toString();
      data['contUnitPrice'].text = data['unitPrice'].trim();
      data['contPriceLevel'].text = data['priceLevel'].toString();
      data['contComment'].text = data['comment'].toString();
      data['comment'] = data['comment'].toString();
      data['contTotal'].text = localCurrencyFormatMain.format(total).trim();
      data['showComment'] = data['comment'] != '' ? true : false;
      data['isManualActivity'] = data['isManualActivity']??false;
      data['isIncludedInComputoMatric'] = data['isIncludedInComputoMatric'] ?? true;
      data['activityDiscountAmount'] = data['activityDiscountAmount'] ?? 0;
      data['activityDiscount'] = data['activityDiscount'] ?? "";

      if (_index > -1) {
        data['index'] = _index;

        if (renovationData[category]![subCategory]!.length >= (_index + 1)) {
          renovationData[category]![subCategory]![_index] = data;
        } else {
          renovationData[category]![subCategory]!.add(data);
        }
      } else {
        renovationData[category]![subCategory]!.add(data);
      }

      data['contUM'].addListener(_checkForChanges);
      data['contQuantity'].addListener(_checkForChanges);
      data['contPriceLevel'].addListener(_checkForChanges);
      data['contUnitPrice'].addListener(_checkForChanges);
      data['contTotal'].addListener(_checkForChanges);
      data['contComment'].addListener(_checkForChanges);

    } catch (e, s) {
      print({e, s});
    }

    subCategoryTotalCalc();
    
  }

  setInitialValues() {
    setState(() {});
  }

  Widget headerRow(Map rowData) {
    return Container(
      // padding: const EdgeInsets.all(8.0),
      padding: const EdgeInsets.only(left: 0, right: 15, top: 3, bottom: 3),
      child: Row(
        mainAxisSize: MainAxisSize.max,
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Expanded(
            flex: 6,
            child: NarFormLabelWidget(
              label: rowData['code'],
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 20,
          ),
          Expanded(
            flex: 1,
            child: NarFormLabelWidget(
              label: 'U.M.',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Expanded(
            flex: 1,
            child: NarFormLabelWidget(
              label: 'Quantità',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          rowData['isManualActivity']
          ? Container()
          : Expanded(
            flex: 1,
            child: NarFormLabelWidget(
              label: 'F. prezzo',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Expanded(
            flex: 2,
            child: NarFormLabelWidget(
              label: 'Unitario',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
          SizedBox(
            width: 5,
          ),
          Expanded(
            flex: 2,
            child: NarFormLabelWidget(
              label: 'Costo',
              fontSize: 10,
              textColor: Color(0xff717171),
            ),
          ),
        ],
      ),
    );
  }

  sortData() {
    renovationData.forEach((category, categoryData) {
      renovationData[category]!.forEach((subCategory, subCategoryData) {
        renovationData[category]![subCategory]!
            .sort((a, b) => a['index'].compareTo(b['index']));
      });
    });
  }

  Widget dataRow(String category,Map rowData) {

    bool isShowCMEIncludeButton = (category == "C - Lavori interni" || category == "M - Forniture");
    String subCategory = rowData['subCategory'];

    int rowIndex = renovationData[category]![subCategory]!.indexWhere((e) {
      return e['index'] == rowData['index'];
    });

    renovationData[category]![subCategory]![rowIndex]['contUM'].text =
        renovationData[category]![subCategory]![rowIndex]['contUM']
            .text
            .toString()
            .toUpperCase();

    Map activityData =
        aef.activitiesForEstimate[category]![subCategory]!.firstWhere(
      (e) => e['activity'] == rowData['title'],
      orElse: () => {},
    );

    bool manual = false;
    // if (activityData.isEmpty) {
    //   manual = true;
    // }

    if( renovationData[category]![subCategory]![rowIndex] ['contPriceLevel'].text == 'Manuale' ) {
      // renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text = '0';
      manual = true;  
      
    }

    return Container(
      decoration: BoxDecoration(
        color: renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ? Color(0xffDBF0E7) : Color(0xffF4F4F4),
        borderRadius: BorderRadius.circular(10),
      ),
      margin: EdgeInsets.only(bottom: 10),
      padding: const EdgeInsets.only(left: 15, right: 15, top: 5, bottom: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Column(
            children: [
              headerRow(renovationData[category]![subCategory]![rowIndex]),
              SizedBox(
                height: 1,
              ),
              Row(
                mainAxisSize: MainAxisSize.max,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    flex: 6,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Tooltip(
                          decoration: BoxDecoration(
                            borderRadius: BorderRadius.circular(7),
                            color: Colors.white,
                            border: Border.all(color: Color(0xffE3E3E3), width: 1 )

                          ),
                          richMessage: WidgetSpan(
                            alignment: PlaceholderAlignment.baseline,
                            baseline: TextBaseline.alphabetic,
                            child: Container(
                              // padding: EdgeInsets.all(10),
                              constraints: const BoxConstraints(maxWidth: 380),
                              child: NarFormLabelWidget(
                                label: renovationData[category]![subCategory]![rowIndex]['title'],
                                fontSize: 11,
                                textColor: Colors.black,
                                overflow: TextOverflow.visible,
                                fontWeight: '600',
                                height: 1.5,
                              ),
                            )
                          ),
                          height: 50,
                          padding: const EdgeInsets.all(15),
                          preferBelow: true,
                          // textStyle: const TextStyle(
                          //   fontSize: 11,
                          // ),
                          showDuration: const Duration(seconds: 2),
                          waitDuration: const Duration(seconds: 1),
                          child: NarFormLabelWidget(
                            label: renovationData[category]![subCategory]![rowIndex]['title'],
                            fontSize: 12,
                            textColor: Colors.black,
                          )
                        ),
                        SizedBox(
                          height: 5,
                        ),

                        Row(
                          children: [
                            if( rowData['isManualActivity'] ) Container(
                              margin: EdgeInsets.only(right:10),
                              child: NarLinkWidget(
                                  fontSize: 12,
                                  //fontWeight: 'bold',
                                  text: 'Modifica',
                                  textDecoration: TextDecoration.underline,
                                  onClick: () {
                                    addVocePopup(category: category,quotationData: renovationData[category]![subCategory]![rowIndex]);
                                  },
                                ),
                            ),
                            if( !rowData['showComment'] ) NarLinkWidget(
                                fontSize: 12,
                                //fontWeight: 'bold',
                                text: 'Aggiungi nota',
                                textDecoration: TextDecoration.underline,
                                onClick: () {
                                  setState(() {
                                    renovationData[category]![subCategory]![
                                        rowIndex]['showComment'] = true;
                                  });
                                },
                              ),

                          ],
                        ),

                        SizedBox(
                          height: 5,
                        ),
                        rowData['showComment'] == true
                            ? Column(
                                mainAxisAlignment: MainAxisAlignment.start,
                                crossAxisAlignment: CrossAxisAlignment.end,
                                children: [
                                  ResizableNoteField(
                                    controllerFontSize: 12,
                                    controller: renovationData[category]![
                                    subCategory]![rowIndex]
                                    ['contComment'],
                                    label: "",
                                    onChanged: (value) {
                                      renovationData[category]![
                                      subCategory]![rowIndex]
                                      ['comment'] = value;
                                    },
                                  ),
                                  SizedBox(
                                    height: 3,
                                  ),
                                  NarLinkWidget(
                                    fontSize: 10,
                                    fontWeight: 'bold',
                                    text: 'Elimina nota',
                                    textDecoration: TextDecoration.underline,
                                    onClick: () {
                                      setState(() {
                                        renovationData[category]![subCategory]![
                                                rowIndex]['contComment']
                                            .text = '';
                                        renovationData[category]![subCategory]![
                                            rowIndex]['comment'] = '';
                                        renovationData[category]![subCategory]![
                                            rowIndex]['showComment'] = false;
                                      });
                                    },
                                  ),
                                ],
                              )
                            : Container()
                      ],
                    ),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Expanded(
                      flex: 1,
                      child: NarSelectBoxWidget(
                          options: unitOfMeasurement,
                          controller: renovationData[category]![subCategory]![rowIndex] ['contUM'],
                          contentPadding: EdgeInsets.only(
                              top: 12, bottom: 12, right: 2, left: 10),
                          onChanged: (value) {
                            renovationData[category]![subCategory]![rowIndex]
                                ['measurementUnit'] = renovationData[category]![
                                    subCategory]![rowIndex]['contUM']
                                .text;
                          })),

                  SizedBox(
                    width: 5,
                  ),

                  CustomTextFormField(
                    flex: 1,
                    suffixIcon: null,
                    label: '',
                    contentPadding: EdgeInsets.only(top: 12, bottom: 12, right: 2,left: 10),
                    // enabled: false,
                    controller: renovationData[category]![subCategory]![rowIndex]['contQuantity'],
                    onChangedCallback: (value) {
                      renovationData[category]![subCategory]![rowIndex]['quantity'] = value;

                      if (isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))) &&
                          isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)) &&
                          renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text != '' &&
                          renovationData[category]![subCategory]![rowIndex]['contQuantity'].text != '') {
                        double unitPrice = double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.replaceAll(".", "").replaceAll(",", ".")) ?? 0.0;
                        double parsedQty = double.tryParse(value.toString()) ?? 0.0;
                        double totalPrice = (unitPrice * parsedQty);
                        renovationData[category]![subCategory]![rowIndex]['contTotal'].text = localCurrencyFormatMain.format(totalPrice);



                        renovationData[category]![subCategory]![rowIndex]['total'] = totalPrice;
                        String activityDiscount = renovationData[category]![subCategory]![rowIndex]['activityDiscount'].toString();
                        if(activityDiscount.isNotEmpty && activityDiscount.contains("%") ){
                            double discountPer = double.tryParse(activityDiscount.replaceAll("%", "")) ?? 0;
                            if (discountPer != 0) {
                              double discountAmount = totalPrice * (discountPer / 100);
                              renovationData[category]![subCategory]![rowIndex]['activityDiscountAmount'] = discountAmount;
                            }
                        }
                      } else if (renovationData[category]![subCategory]![rowIndex]['contQuantity'].text == '') {
                        renovationData[category]![subCategory]![rowIndex]['total'] = 0;
                        renovationData[category]![subCategory]![rowIndex]['activityDiscountAmount'] = 0;
                      }

                      subCategoryTotalCalc();

                      setState(() {});
                    },
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                    ],
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  if( !rowData['isManualActivity'] ) Expanded(
                    flex: 1,
                    child:
                    NarSelectBoxWidget(
                      options: priceLevel,
                      controller: renovationData[category]![subCategory]![rowIndex]['contPriceLevel'],
                      onChanged: (value) {

                        renovationData[category]![subCategory]![rowIndex] ['priceLevel'] = renovationData[category]![subCategory]![rowIndex] ['contPriceLevel'].text;

                        if( renovationData[category]![subCategory]![rowIndex] ['contPriceLevel'].text == 'Manuale' ) {
                          renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text = '0';

                          manual = true;
                          setState(() {});
                          subCategoryTotalCalc();
                          return;
                        }

                        Map activityPrices = aef
                            .activitiesForEstimate[category]![subCategory]!
                            .firstWhere((value) =>
                                value['activity'] == rowData['title']);

                        final unitPrice = activityPrices[renovationData[
                                        category]![subCategory]![rowIndex]
                                    ['priceLevel']
                                .toLowerCase()]
                            .toString();

                        renovationData[category]![subCategory]![rowIndex]['unitPrice'] = localCurrencyFormatMain.format(double.tryParse(unitPrice)).trim();
                        renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text = localCurrencyFormatMain.format(double.tryParse(unitPrice)).trim();

                        if (isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))) &&
                            isNumber(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text) &&
                            renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text != '' &&
                            renovationData[category]![subCategory]![rowIndex]['contQuantity'].text != '') {
                          renovationData[category]![subCategory]![rowIndex]['contTotal'].text = localCurrencyFormatMain.format((double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))! *
                                  double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)!));

                          double newTotal = double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))! *
                              double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)!;
                          renovationData[category]![subCategory]![rowIndex]['total'] = newTotal;
                        }
                        subCategoryTotalCalc();

                        setState(() {
                          manual = true;
                        });
                      },
                      validationType: 'required',
                      parametersValidate: 'Required!',
                      contentPadding: EdgeInsets.only(
                          top: 12, bottom: 12, right: 2, left: 10),
                    ),
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  CustomTextFormField(
                      flex: 2,
                      label: '',
                      inputFormatters: [
                        FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                      ],
                      isMoney: true,
                      isShowPrefillMoneyIcon: false,
                      controller: renovationData[category]![subCategory]![rowIndex]['contUnitPrice'],
                      contentPadding: EdgeInsets.only(
                          top: 12, bottom: 12, right: 2, left: 10),
                      enabled: manual || rowData['isManualActivity'],
                      onChangedCallback: (value) {
                        final unitPrice = renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text;
                        renovationData[category]![subCategory]![rowIndex]['unitPrice'] = unitPrice;

                        if (isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text.toString().replaceAll(".", "").replaceAll(",", "."))) &&
                            isNumber(double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text)) &&
                            renovationData[category]![subCategory]![rowIndex]['contUnitPrice'].text != '' &&
                            renovationData[category]![subCategory]![rowIndex]['contQuantity'].text != '') {
                          double qty = double.tryParse(renovationData[category]![subCategory]![rowIndex]['contQuantity'].text) ?? 0.0;
                          double parsedUnitPrice = double.tryParse(value.toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0.0;
                          double totalPrice = (qty * parsedUnitPrice);

                          String activityDiscount = renovationData[category]![subCategory]![rowIndex]['activityDiscount'].toString();
                          if(activityDiscount.isNotEmpty && activityDiscount.contains("%")){
                              double discountPer = double.tryParse(activityDiscount.replaceAll("%", "")) ?? 0;
                              if (discountPer != 0) {
                                double discountAmount = totalPrice * (discountPer / 100);
                                renovationData[category]![subCategory]![rowIndex]['activityDiscountAmount'] = discountAmount;
                              }
                          }

                          renovationData[category]![subCategory]![rowIndex]['contTotal'].text = localCurrencyFormatMain.format(totalPrice);

                          renovationData[category]![subCategory]![rowIndex]['total'] = totalPrice;
                        }

                        subCategoryTotalCalc();

                        setState(() {});
                      },
                      suffixIcon: Column(
                        mainAxisSize: MainAxisSize.max,
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              NarFormLabelWidget(
                                label: '€',
                                fontSize: 14,
                                textColor: Colors.black,
                              ),
                              SizedBox(
                                width: 5,
                              )
                            ],
                          ),
                        ],
                      )),
                  SizedBox(
                    width: 5,
                  ),
                  CustomTextFormField(
                    flex: 2,
                    label: '',
                    enabled: false,
                    isMoney: true,
                    inputFormatters: [
                      FilteringTextInputFormatter.allow(RegExp(r'[0-9,.]')),
                    ],
                    controller: renovationData[category]![subCategory]![rowIndex]['contTotal'],
                    contentPadding: EdgeInsets.only(
                        top: 12, bottom: 12, right: 2, left: 10),
                    suffixIcon: Column(
                      mainAxisSize: MainAxisSize.max,
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            NarFormLabelWidget(
                              label: '€',
                              fontSize: 14,
                              textColor: Colors.black,
                            ),
                            SizedBox(
                              width: 5,
                            )
                          ],
                        ),
                      ],
                    ),
                    isShowPrefillMoneyIcon: false,
                    // enabled: false,
                  ),
                  // SizedBox(width: 5,),
                ],
              ),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Row(
                    children: [
                      Container(
                        height: 25,
                        width: 25,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(227, 227, 227, 1),
                          borderRadius: BorderRadius.circular(7.0),
                        ),
                        child: IconButton(
                          onPressed: () {
                            if (rowIndex == 0) {
                              renovationData[category]![subCategory]![rowIndex]['index'] =
                                  renovationData[category]![subCategory]!.length - 1;
                              for (var i = 1; i < renovationData[category]![subCategory]!.length; i++) {
                                renovationData[category]![subCategory]![i]['index'] =
                                    renovationData[category]![subCategory]![i]['index'] - 1;
                              }
                            } else {
                              int _index = renovationData[category]![subCategory]![rowIndex]['index'];

                              renovationData[category]![subCategory]![rowIndex]['index'] = renovationData[category]![subCategory]![rowIndex]['index'] - 1;

                              renovationData[category]![subCategory]![rowIndex - 1]['index'] = _index;
                            }

                            sortData();
                            setState(() {});
                          },
                          style: ButtonStyle(
                              overlayColor:
                                  WidgetStateProperty.all(Colors.transparent)),
                          padding: EdgeInsets.zero,
                          icon: Transform.rotate(
                            angle: -1.5708,
                            child: SvgPicture.asset(
                              "assets/icons/cash_in.svg",
                              color: Color(0xff5b5b5b),
                            ),
                          ),
                        ),
                      ),
                      SizedBox(width: 5),
                      Container(
                        height: 25,
                        width: 25,
                        decoration: BoxDecoration(
                          color: Color.fromRGBO(227, 227, 227, 1),
                          borderRadius: BorderRadius.circular(7.0),
                        ),
                        child: IconButton(
                          onPressed: () {
                            if (rowIndex ==
                                renovationData[category]![subCategory]!.length -
                                    1) {
                              renovationData[category]![subCategory]![rowIndex]
                                  ['index'] = 0;
                              for (var i = 0;
                                  i <
                                      renovationData[category]![subCategory]!
                                              .length -
                                          1;
                                  i++) {
                                renovationData[category]![subCategory]![i]
                                        ['index'] =
                                    renovationData[category]![subCategory]![i]
                                            ['index'] +
                                        1;
                              }
                            } else {
                              int _index = renovationData[category]![
                                  subCategory]![rowIndex]['index'];

                              renovationData[category]![subCategory]![rowIndex]
                                  ['index'] = renovationData[category]![
                                      subCategory]![rowIndex]['index'] +
                                  1;

                              renovationData[category]![subCategory]![
                                  rowIndex + 1]['index'] = _index;
                            }

                            sortData();
                            setState(() {});
                          },
                          style: ButtonStyle(
                              overlayColor:
                                  WidgetStateProperty.all(Colors.transparent)),
                          padding: EdgeInsets.zero,
                          icon: Transform.rotate(
                            angle: 1.5708,
                            child: SvgPicture.asset(
                              "assets/icons/cash_in.svg",
                              color: Color(0xff5b5b5b),
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                  SizedBox(
                    width: 5,
                  ),
                  Row(
                    crossAxisAlignment: CrossAxisAlignment.center,
                    mainAxisAlignment: MainAxisAlignment.end,
                    children: [
                      Visibility(
                        visible: renovationData[category]![subCategory]![rowIndex]['isDiscounted'],
                        child: Padding(
                          padding: const EdgeInsets.only(right: 10),
                          child: NarFormLabelWidget(
                            label: '-${localCurrencyFormatMain.format(renovationData[category]![subCategory]![rowIndex]['activityDiscountAmount']).trim()}€',
                            fontSize: 12,
                            fontWeight: "600",
                            textColor: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),
                      IconTextButtonWidget(
                        iconOnLeft: false,
                        iconHeight: 7,
                        textStyle: TextStyle(
                            fontSize: 10,
                            fontFamily: 'Raleway-600',
                            color: renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ? Colors.white : Colors.black
                        ),
                        text: renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ? "Rimuovi sconto" : "Sconta",
                        backgroundColor: renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ? Theme.of(context).primaryColor : Colors.white,
                        iconColor: renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ? Colors.white : Colors.black,
                        onPressed: () {
                          if( renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ) {
                            renovationData[category]![subCategory]![rowIndex]['isDiscounted'] = false;
                            renovationData[category]![subCategory]![rowIndex]['activityDiscountAmount'] = 0;
                            renovationData[category]![subCategory]![rowIndex]['activityDiscount'] = "";
                            subCategoryTotalCalc();
                            setState(() {});
                          } else {
                            scontaVoceDialog(category: category,subCategory: subCategory,rowIndex: rowIndex);
                          }

                        },
                        borderColor: renovationData[category]![subCategory]![rowIndex]['isDiscounted'] ? Colors.transparent : Color(0xFFE1E1E1),
                        icon: 'assets/icons/discount.svg',
                      ),
                      SizedBox(width: 10),

                      Visibility(
                        visible: isShowCMEIncludeButton,
                        child: IconTextButtonWidget(
                          iconOnLeft: false,
                          // height: 20,
                          iconHeight: 7,
                          textStyle: TextStyle(
                            fontSize: 10,
                            fontFamily: 'Raleway-600',
                            color: renovationData[category]![subCategory]![rowIndex]['isIncludedInComputoMatric'] ? Colors.black : Colors.white
                          ),
                          text: "CME",
                          backgroundColor: renovationData[category]![subCategory]![rowIndex]['isIncludedInComputoMatric'] ? Colors.white :Color(0xFFE82525),
                          iconColor: renovationData[category]![subCategory]![rowIndex] ['isIncludedInComputoMatric'] ? Colors.black : Colors.white,
                          onPressed: (){
                            setState(() {
                              renovationData[category]![subCategory]![rowIndex] ['isIncludedInComputoMatric'] = !renovationData[category]![subCategory]![rowIndex] ['isIncludedInComputoMatric'];
                            });
                          },
                          borderColor: renovationData[category]![subCategory]![rowIndex] ['isIncludedInComputoMatric'] ? Color(0xFFE1E1E1) : Colors.transparent,
                          icon: renovationData[category]![subCategory]![rowIndex] ['isIncludedInComputoMatric'] ? 'assets/icons/eye.svg' : 'assets/icons/closed-eye.svg',
                        ),
                      ),
                      SizedBox(width: 5),
                      TextButton(
                        child: Container(
                          child: Image.asset('assets/icons/trash-process.png',
                              color: Color(0xff9B9B9B), height: 20),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.transparent,
                            borderRadius: BorderRadius.circular(7.0),
                          ),
                        ),
                        onPressed: () {
                          showDialog(
                              context: context,
                              builder: (BuildContext _bc1) {
                                return StatefulBuilder(builder:
                                    (BuildContext _bc2, StateSetter setState) {
                                  return Center(
                                      child: BaseNewarcPopup(
                                    formErrorMessage: formMessages,
                                    buttonText: 'Ok',
                                    onPressed: () async {
                                      try {
                                        categoryTotal[category] -= double.tryParse(
                                            renovationData[category]![subCategory]![
                                                    rowIndex]['total']
                                                .toString().replaceAll(".", "").replaceAll(",", "."));
                                        renovationData[category]![subCategory]!
                                            .removeAt(rowIndex);

                                        if (renovationData[category]![subCategory]!
                                                .length ==
                                            0) {
                                          renovationData[category]!.removeWhere(
                                              (e, value) => e == subCategory);
                                        }

                                        if (renovationData[category]!.length == 0) {
                                          renovationData.removeWhere(
                                              (e, value) => e == category);
                                        }
                                      } catch (e, s) {
                                        print({e, s});
                                      }
                                      subCategoryTotalCalc();
                                      setInitialValues();
                                      return true;
                                    },
                                    title: "Attenzione!",
                                    column: Container(
                                      width: 400,
                                      margin: EdgeInsets.symmetric(vertical: 30),
                                      child: Center(
                                        child: ListView(
                                          shrinkWrap: true,
                                          children: [
                                            NarFormLabelWidget(
                                              label:
                                                  "Vuoi davvero eliminare questo?",
                                              fontSize: 18,
                                              textAlign: TextAlign.center,
                                              fontWeight: '600',
                                            )
                                          ],
                                        ),
                                      ),
                                    ),
                                  ));
                                });
                              });
                        },
                        style: ButtonStyle(
                          overlayColor: WidgetStateProperty.all(Colors.transparent),
                          splashFactory:
                              NoSplash.splashFactory, // Remove the splash effect
                          padding: WidgetStateProperty.all(
                              EdgeInsets.zero), // Remove padding
                          minimumSize: WidgetStateProperty.all(
                              Size.zero), // Remove minimum size constraints
                          tapTargetSize: MaterialTapTargetSize.shrinkWrap,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ],
          )
        ],
      ),
    );
  }

  addVocePopup({category = null, quotationData = null}) {
    selectedActivities.clear();

    if ( quotationData == null) {
      contActivity!.text = '';
      contSubCategory!.text = '';
      contCategory!.text = '';
      contManualActivity!.text = '';
      contDescription!.text = '';
      contUM!.text = '';
      contQuantity!.text = '1';
      contUnitCost!.text = '';
      contTotal!.text = '';
      addManualActivity = false;
    } else {
      try {
        contActivity!.text = '';
        contSubCategory!.text = '';


        setState(() {});

        contCategory!.text = category;
        setSubCategories();
        if (quotationData['subCategory'] != '') {
          contSubCategory!.text = quotationData['subCategory'];
        } else {
          contSubCategory!.clear();
        }
        setActivities();


        int selectedActivity =
            activities.indexWhere((e){
              return e['value'] == quotationData['title'];
            });
        contActivity!.text =
            selectedActivity > -1 ? quotationData['title'] : '';
        contManualActivity!.text =
            selectedActivity == -1 ? quotationData['title'] : '';
        contDescription!.text = quotationData['description'];
        contUM!.text = quotationData['measurementUnit'];
        contQuantity!.text = quotationData['quantity'].toString();
        contUnitCost!.text = quotationData['unitPrice'].toString();
        contTotal!.text = quotationData['total'].toString();
        addManualActivity = selectedActivity == -1 ? true : false;


        if (quotationData['priceLevel'] != '') {
          contPriceLevel!.text = quotationData['priceLevel'];
        } else {
          contPriceLevel!.clear();
        }
      } catch (e, s) {
        print({e, s});
      }
    }

    setState(() {
      formMessages.clear();
      formMessages.add('');
    });

    return showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (context, setState) {
            return Center(
                child: BaseNewarcPopup(
                    formErrorMessage: formMessages,
                    title: quotationData == null ? 'Aggiungi voce' : 'Modifica voce',
                    buttonText: quotationData == null ? "Inserisci voce" : "Modifica voce",
                    onPressed: () {
                      if (selectedActivities.length > 0) {
                        for (var i = 0; i < selectedActivities.length; i++) {
                          try {
                            Map activityData = aef.activitiesForEstimate[
                                    contCategory!.text]![contSubCategory!.text]!
                                .firstWhere((e) =>
                                    e['activity'] ==
                                    selectedActivities[i]['value']);

                            Map rowData = {
                              'title': addManualActivity
                                  ? contManualActivity!.text
                                  : selectedActivities[i]['value'],
                              'subCategory': contSubCategory!.text,
                              'measurementUnit': activityData['um'],
                              'quantity': '1',
                              'unitPrice': localCurrencyFormatMain.format(activityData['f1']),
                              'description': '',
                              'priceLevel': 'F1',
                              'code': !addManualActivity
                                  ? activityData['code']
                                  : '',
                              'comment': '',
                              'isManualActivity': addManualActivity
                            };

                            if (quotationData != null) {
                              rowData['index'] = quotationData['index'];
                            }

                            rowData['isDiscounted'] = false;

                            // print({ 'rowData', rowData });
                            setDataRow(contCategory!.text, rowData);
                          } catch (e, s) {
                            print({e, s});
                          }

                          // setDataRow(contCategory!.text, rowData);
                        }
                      }else if (addManualActivity && quotationData != null) {
                        Map rowData = {
                          'title': contManualActivity!.text,
                          'isManualActivity': addManualActivity,
                          'subCategory': contSubCategory!.text,
                          'measurementUnit': contUM!.text,
                          'quantity': contQuantity!.text,
                          'unitPrice': contUnitCost!.text,
                          'description': contDescription!.text,
                          'priceLevel': contPriceLevel!.text,
                          'code': '',
                          'comment': contComment!.text
                        };

                        if (quotationData != null) {
                          rowData['index'] = quotationData['index'];
                        }

                        rowData['isDiscounted'] = false;

                        // print({ 'rowData', rowData });
                        setDataRow(contCategory!.text, rowData);
                      } else if (addManualActivity) {
                        Map rowData = {
                          'title': contManualActivity!.text,
                          'isManualActivity': addManualActivity,
                          'subCategory': contSubCategory!.text,
                          'measurementUnit': '',
                          'quantity': '1',
                          'unitPrice': '0',
                          'description': '',
                          'priceLevel': 'F1',
                          'code': '',
                          'comment': ''
                        };

                        if (quotationData != null) {
                          rowData['index'] = quotationData['index'];
                        }

                        rowData['isDiscounted'] = false;

                        // print({ 'rowData', rowData });
                        setDataRow(contCategory!.text, rowData);
                      }

                      setInitialValues();
                    },
                    column: Container(
                        width: 600,
                        child: Column(
                            children: [
                          Row(
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Seleziona categoria',
                                        fontSize: 13,
                                        textColor: Color(0xff696969),
                                        fontWeight: '600',
                                      ),
                                      SizedBox(
                                        height: 4,
                                      ),
                                      NarSelectBoxWidget(
                                        options: categories,
                                        controller: contCategory,
                                        validationType: 'required',
                                        parametersValidate: 'Required!',
                                        contentPadding: EdgeInsets.only(
                                            top: 15,
                                            bottom: 17,
                                            left: 21.0,
                                            right: 8.0),
                                        onChanged: (value) {
                                          selectedActivities.clear();
                                          contSubCategory!.clear();
                                          contActivity!.clear();
                                          contPriceLevel!.clear();
                                          contUnitCost!.clear();
                                          contTotal!.clear();

                                          setSubCategories();
                                          setState(() {});
                                        },
                                      )
                                    ],
                                  )),
                              SizedBox(
                                width: 10,
                              ),
                              Expanded(
                                  flex: 1,
                                  child: Column(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      NarFormLabelWidget(
                                        label: 'Seleziona sottocategoria',
                                        fontSize: 13,
                                        textColor: Color(0xff696969),
                                        fontWeight: '600',
                                      ),
                                      SizedBox(
                                        height: 4,
                                      ),
                                      NarSelectBoxWidget(
                                        options: subCategories,
                                        controller: contSubCategory,
                                        validationType: 'required',
                                        parametersValidate: 'Required!',
                                        contentPadding: EdgeInsets.only(
                                            top: 15,
                                            bottom: 17,
                                            left: 21.0,
                                            right: 8.0),
                                        onChanged: (value) {
                                          selectedActivities.clear();
                                          contActivity!.clear();
                                          contPriceLevel!.clear();
                                          contUnitCost!.clear();
                                          contTotal!.clear();

                                          setActivities();
                                          setState(() {});
                                        },
                                      )
                                    ],
                                  )),
                            ],
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            crossAxisAlignment: CrossAxisAlignment.end,
                            children: [
                              Expanded(
                                  flex: 1,
                                  child: Opacity(
                                    opacity: addManualActivity ? 0.5 : 1,
                                    child: AbsorbPointer(
                                      absorbing: addManualActivity,
                                      child: Column(
                                        crossAxisAlignment:
                                            CrossAxisAlignment.start,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Seleziona attività',
                                            fontSize: 13,
                                            textColor: Color(0xff696969),
                                            fontWeight: '600',
                                          ),
                                          SizedBox(
                                            height: 4,
                                          ),
                                          MultiSelectDropdownWidget(
                                            options: activities,
                                            initialValue: selectedActivities,
                                            validationType: 'required',
                                            parametersValidate: 'Obbligatorio',
                                            onChanged:
                                                (List<dynamic> selectedValues) {
                                              changedDetected = true;
                                              selectedActivities =
                                                  selectedValues;
                                              setState(() {});
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  )),

                              // SizedBox(width: 10,),
                            ],
                          ),
                          SizedBox(
                            height: 10,
                          ),
                          Row(
                            mainAxisAlignment: MainAxisAlignment.end,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: 'oppure',
                                textColor: Color(0xff727272),
                                fontSize: 14,
                                textAlign: TextAlign.right,
                              ),
                              SizedBox(
                                width: 5,
                              ),
                              BaseNewarcButton(
                                  buttonText: 'Aggiungi manuale',
                                  fontSize: 14,
                                  textColor: Colors.black,
                                  fontWeight: 'bold',
                                  color: Color(0xffE6E6E6),
                                  notAccent: !addManualActivity,
                                  onPressed: () {
                                    setState(() {
                                      addManualActivity = !addManualActivity;

                                      selectedActivities.clear();

                                      contActivity!.clear();
                                      contPriceLevel!.clear();
                                      contUnitCost!.clear();
                                      contTotal!.clear();

                                      if (addManualActivity == true) {
                                        contActivity!.text = '';
                                      }
                                    });
                                  }),
                            ],
                          ),
                          addManualActivity == false
                              ? SizedBox(
                                  height: 0,
                                )
                              : SizedBox(
                                  height: 10,
                                ),
                          addManualActivity == false
                              ? SizedBox(
                                  height: 0,
                                )
                              : Wrap(
                                  // crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    Container(
                                      margin: EdgeInsets.only(bottom: 4),
                                      child: Row(
                                        mainAxisAlignment:
                                            MainAxisAlignment.spaceBetween,
                                        children: [
                                          NarFormLabelWidget(
                                            label: 'Aggiungi attività manuale',
                                            fontSize: 14,
                                            textColor: Color(0xff696969),
                                            fontWeight: '600',
                                          ),
                                          GestureDetector(
                                            onTap: () {
                                              contActivity!.clear();
                                              contPriceLevel!.clear();
                                              contUnitCost!.clear();
                                              contTotal!.clear();

                                              setState(() {
                                                addManualActivity = !addManualActivity;
                                              });
                                            },
                                            child: MouseRegion(
                                              cursor: SystemMouseCursors.click,
                                              child: NarFormLabelWidget(
                                                label: 'Elimina',
                                                textDecoration:
                                                    TextDecoration.underline,
                                                fontSize: 14,
                                                textColor: Color(0xffC24040),
                                                fontWeight: '600',
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    ResizableNoteField(
                                      controller: contManualActivity,
                                      label: "",
                                    )

                                  ],
                                ),
                          addManualActivity == false
                              ? SizedBox(
                                  height: 10,
                                )
                              : SizedBox(
                                  height: 0,
                                ),

                          
                        ]))));
          });
        });
  }
  final NumberFormat currencyFormat = NumberFormat("#,##0.00", "it_IT");

  @override
  Widget build(BuildContext context) {
    return Container(
      height: double.infinity,
      color: Color(0XFFF9F9F9),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.max,
        children: [
          Row(
              mainAxisSize: MainAxisSize.min,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                IconButton(
                  hoverColor: Colors.transparent,
                  focusColor: Colors.transparent,
                  onPressed: () {
                    if (changedDetected == false) {
                      widget.updateViewCallback!('renovation-quotation');
                    } else {
                      showDialog(
                          context: context,
                          builder: (_context) {
                            return StatefulBuilder(builder:
                                (BuildContext _bc2,
                                StateSetter setState) {
                              return Center(
                                child: BaseNewarcPopup(
                                  title: 'Attenzione!',
                                  buttonText: 'Esci senza salvare',
                                  onPressed: () async {
                                    widget.updateViewCallback!(
                                        'renovation-quotation');
                                    return true;
                                  },
                                  column: Container(
                                      height: 150,
                                      width: 465,
                                      child: Center(
                                        child: Column(
                                          children: [
                                            NarFormLabelWidget(
                                                overflow:
                                                TextOverflow.visible,
                                                label:
                                                "Stai uscendo dalla pagina senza aver\nsalvato le modifiche.",
                                                textAlign:
                                                TextAlign.center,
                                                fontSize: 18,
                                                fontWeight: '600',
                                                height: 1.5,
                                                textColor:
                                                Color(0xFF696969)),
                                            SizedBox(
                                              height: 10,
                                            ),
                                            NarFormLabelWidget(
                                                overflow:
                                                TextOverflow.visible,
                                                label:
                                                "Vuoi uscire senza salvare?",
                                                textAlign:
                                                TextAlign.center,
                                                fontSize: 18,
                                                fontWeight: 'bold',
                                                height: 1.5,
                                                textColor:
                                                Color(0xFF696969)),
                                          ],
                                        ),
                                      )),
                                ),
                              );
                            });
                          });
                    }
                  },
                  icon: SvgPicture.asset('assets/icons/arrow_left.svg',
                      height: 20, color: Colors.black),
                ),
                SizedBox(
                  width: 10,
                ),
                NarFormLabelWidget(
                  label: widget.renovationQuotation!.renovationContact!.addressInfo != null
                      ? "${widget.renovationQuotation!.renovationContact!.addressInfo!.toShortAddress()}"
                      : widget.renovationQuotation!.renovationContact!.streetAddress!,
                  fontSize: 22,
                  fontWeight: 'bold',
                  textColor: Colors.black,
                ),
              ]),
          SizedBox(height: 20),
          Expanded(
            child: Form(
              key: _formKey,
              child: Container(
                color: Color(0XFFF9F9F9),
                child: ListView(
                  // shrinkWrap: true,
                  children: [
                    //------Preventivo ristrutturazione
                    Container(
                      padding: EdgeInsets.all(15),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: 'Preventivo ristrutturazione',
                                fontSize: 20,
                                fontWeight: 'bold',
                              ),
                              Row(
                                children: [
                                  BaseNewarcButton(
                                    color: Colors.transparent,
                                    textColor: Theme.of(context).primaryColor.withOpacity(isLoadingPagamenti ? 0.5 : 1),
                                    borderColor: Theme.of(context).primaryColor.withOpacity(isLoadingPagamenti ? 0.5 : 1),
                                    buttonText: "Download PDF",
                                    onPressed: isLoadingPagamenti ? (){} : () async {

                                      List<String>? cat = widget.renovationQuotation?.pagamento
                                          ?.map((paga) => paga.categoryName ?? "")
                                          .where((e) => e.isNotEmpty)
                                          .toList();

                                      Map<String, bool> categoryMap = Map.fromEntries(
                                        renovationCategoryMap.entries.where((entry) =>
                                        entry.key == "Completo" || (cat?.contains(entry.key) ?? false)),
                                      );

                                      showSelectCategoryForPreventivoDialog(context: context, categoryMap: categoryMap, selectedRenovationQuotation: widget.renovationQuotation!);
                                    }
                                  ),
                                  SizedBox(width: 10),
                                  BaseNewarcButton(
                                      buttonText: "Aggiungi voce",
                                      onPressed: () {
                                        addVocePopup();
                                      }),
                                ],
                              )
                            ],
                          ),
                          SizedBox(height: 30),
                          renovationData.length == 0
                              ? SizedBox(height: 0)
                              : Column(
                                  children: categoryOrder.map((category) {
                                    bool isExpanded = expandedCategories[category] ?? false;
                                    int index = categoryOrder.indexOf(category);
                                    return Wrap(
                                      children: [
                                        Container(
                                          margin: EdgeInsets.only(top: 15),
                                          padding: EdgeInsets.all(20),
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.only(
                                                  topLeft: Radius.circular(13),
                                                  topRight: Radius.circular(13),
                                                  bottomRight: !isExpanded ?  Radius.circular(13) : Radius.zero,
                                                  bottomLeft: !isExpanded ?  Radius.circular(13) : Radius.zero,

                                              ),
                                              border: Border.all(
                                                  width: 1,
                                                  color: Color(0Xffe7e7e7))),
                                          child: Row(
                                            mainAxisSize: MainAxisSize.max,
                                            mainAxisAlignment:
                                                MainAxisAlignment.spaceBetween,
                                            crossAxisAlignment:
                                                CrossAxisAlignment.center,
                                            children: [
                                              Row(
                                                children: [
                                                  //------Up Move
                                                  Container(
                                                    height: 25,
                                                    width: 25,
                                                    decoration: BoxDecoration(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      borderRadius: BorderRadius.circular(7.0),
                                                    ),
                                                    child: IconButton(
                                                      onPressed: index > 0 ? () => moveCategory(category, true) : null,
                                                      style: ButtonStyle(
                                                          overlayColor: WidgetStateProperty.all(Colors.transparent)),
                                                      padding: EdgeInsets.zero,
                                                      icon: Transform.rotate(
                                                        angle: -1.5708,
                                                        child: SvgPicture.asset(
                                                            "assets/icons/cash_in.svg",
                                                          color: Color(0xff5b5b5b),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 5),

                                                  //------- Down Move
                                                  Container(
                                                    height: 25,
                                                    width: 25,
                                                    decoration: BoxDecoration(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      borderRadius: BorderRadius.circular(7.0),
                                                    ),
                                                    child: IconButton(
                                                      onPressed: index < categoryOrder.length - 1 ? () => moveCategory(category, false) : null,
                                                      style: ButtonStyle(
                                                          overlayColor:
                                                          WidgetStateProperty.all(Colors.transparent)),
                                                      padding: EdgeInsets.zero,
                                                      icon: Transform.rotate(
                                                        angle: 1.5708,
                                                        child: SvgPicture.asset(
                                                          "assets/icons/cash_in.svg",
                                                          color: Color(0xff5b5b5b),
                                                        ),
                                                      ),
                                                    ),
                                                  ),
                                                  SizedBox(width: 10,),
                                                  NarFormLabelWidget(
                                                    label: category,
                                                    fontSize: 18,
                                                    fontWeight: 'bold',
                                                    textColor: Theme.of(context).primaryColor,
                                                  ),
                                                ],
                                              ),


                                              SizedBox(
                                                width: 200,
                                                child: Row(
                                                  mainAxisSize: MainAxisSize.min,
                                                  children: [
                                                    NarFormLabelWidget(
                                                      label: 'Forza fascia',
                                                      fontSize: 14,
                                                      fontWeight: '600',
                                                      textColor: AppColor.black,
                                                    ),
                                                    SizedBox(width: 10),
                                                    Expanded(
                                                      child: NarSelectBoxWidget(
                                                        options: ['F1', 'F2', 'F3'],
                                                        contentPadding: EdgeInsets.only(top: 12, bottom: 12, right: 2, left: 10),
                                                        controller: categoryControllers[category],
                                                          onChanged: (value) {
                                                            if (renovationData.containsKey(category)) {
                                                              renovationData[category]!.forEach((subCategory, rows) {
                                                                for (var row in rows) {
                                                                  // Skip rows with 'Manuale' price level
                                                                  if (row['contPriceLevel'].text == 'Manuale') {
                                                                    log("Skipping update for row with Manuale price level");
                                                                    continue;
                                                                  }

                                                                  row['contPriceLevel'].text = categoryControllers[category]?.text;
                                                                  row['priceLevel'] = categoryControllers[category]?.text;

                                                                  Map? activityPrices = aef.activitiesForEstimate[category]?[subCategory]?.firstWhere(
                                                                        (value) => value['activity'] == row['title'],
                                                                    orElse: () => {},
                                                                  );

                                                                  if (activityPrices != null && activityPrices.isNotEmpty) {

                                                                    final unitPrice = activityPrices[categoryControllers[category]?.text.toLowerCase()] ?? 0;
                                                                    row['unitPrice'] = localCurrencyFormatMain.format(unitPrice).trim();
                                                                    row['contUnitPrice'].text = localCurrencyFormatMain.format(unitPrice).trim();

                                                                    if (isNumber(unitPrice) &&
                                                                        isNumber(row['contQuantity'].text) &&
                                                                        row['contUnitPrice'].text.isNotEmpty &&
                                                                        row['contQuantity'].text.isNotEmpty) {
                                                                      row['contTotal'].text = localCurrencyFormatMain.format((unitPrice *
                                                                          double.parse(row['contQuantity'].text)));
                                                                      row['total'] = row['contTotal'].text;
                                                                    }
                                                                  } else {
                                                                    log("No matching activity price found for ${row['title']}");
                                                                  }
                                                                }
                                                              });

                                                              subCategoryTotalCalc();

                                                              // Refresh UI
                                                              setState(() {});
                                                            } else {
                                                              log("Category not found in renovationData: $category");
                                                            }
                                                          },
                                                      ),
                                                    ),
                                                  ],
                                                ),
                                              ),

                                              Row(
                                                children: [
                                                  NarFormLabelWidget(
                                                    label: localCurrencyFormatMain.format(categoryTotal[category] ?? 0) + '€',
                                                    fontSize: 16,
                                                    fontWeight: 'bold',
                                                    textColor: Theme.of(context).primaryColor,
                                                  ),
                                                  SizedBox(width: 10),
                                                  IconButton(
                                                    hoverColor: Colors.transparent,
                                                    focusColor: Colors.transparent,
                                                    onPressed: (){
                                                      toggleCategory(category);
                                                    },
                                                    icon: isExpanded ? Icon(Icons.expand_less,color: AppColor.black,) : Icon(Icons.expand_more,color: AppColor.black,),
                                                  ),
                                                ],
                                              )
                                            ],
                                          ),
                                        ),
                                        if (isExpanded)
                                        Container(
                                          padding: const EdgeInsets.symmetric(
                                              horizontal: 15, vertical: 10),
                                          decoration: BoxDecoration(
                                              color: Colors.white,
                                              borderRadius: BorderRadius.only(
                                                  bottomLeft: Radius.circular(13),
                                                  bottomRight:
                                                      Radius.circular(13)),
                                              border: Border(
                                                left: BorderSide(
                                                    width: 1.0,
                                                    color: Color(0Xffe7e7e7)),
                                                right: BorderSide(
                                                    width: 1.0,
                                                    color: Color(0Xffe7e7e7)),
                                                bottom: BorderSide(
                                                    width: 1.0,
                                                    color: Color(0Xffe7e7e7)),
                                              )),
                                          child: Column(
                                            children: [
                                              Wrap(
                                                children:
                                                    renovationData[category]!.keys
                                                        .map((subcategory) {
                                                      List<String> subCategoryKeys = renovationData[category]!.keys.toList();
                                                      int subcategoryIndex = subCategoryKeys.indexOf(subcategory);
                                                  return Column(
                                                    crossAxisAlignment:
                                                        CrossAxisAlignment.start,
                                                    children: [
                                                      SizedBox(height: 5),
                                                      Row(
                                                        children: [
                                                          //------Up Move
                                                          Container(
                                                            height: 25,
                                                            width: 25,
                                                            decoration: BoxDecoration(
                                                              color: Color.fromRGBO(227, 227, 227, 1),
                                                              borderRadius: BorderRadius.circular(7.0),
                                                            ),
                                                            child: IconButton(
                                                              onPressed: subcategoryIndex > 0 ? () => moveSubCategory(category,subcategory, true) : null,
                                                              style: ButtonStyle(
                                                                  overlayColor:
                                                                  WidgetStateProperty.all(Colors.transparent)),
                                                              padding: EdgeInsets.zero,
                                                              icon: Transform.rotate(
                                                                angle: -1.5708,
                                                                child: SvgPicture.asset(
                                                                  "assets/icons/cash_in.svg",
                                                                  color: Color(0xff5b5b5b),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(width: 5),

                                                          //------- Down Move
                                                          Container(
                                                            height: 25,
                                                            width: 25,
                                                            decoration: BoxDecoration(
                                                              color: Color.fromRGBO(227, 227, 227, 1),
                                                              borderRadius: BorderRadius.circular(7.0),
                                                            ),
                                                            child: IconButton(
                                                              onPressed: subcategoryIndex < renovationData[category]!.keys.length - 1 ? () => moveSubCategory(category,subcategory, false) : null,
                                                              style: ButtonStyle(
                                                                  overlayColor:
                                                                  WidgetStateProperty.all(Colors.transparent)),
                                                              padding: EdgeInsets.zero,
                                                              icon: Transform.rotate(
                                                                angle: 1.5708,
                                                                child: SvgPicture.asset(
                                                                  "assets/icons/cash_in.svg",
                                                                  color: Color(0xff5b5b5b),
                                                                ),
                                                              ),
                                                            ),
                                                          ),
                                                          SizedBox(width: 10),
                                                          NarFormLabelWidget(
                                                            label: subcategory,
                                                            fontSize: 15,
                                                            fontWeight: 'bold',
                                                            textColor: Colors.black,
                                                          ),
                                                        ],
                                                      ),
                                                      SizedBox(height: 15),
                                                      ...renovationData[
                                                                  category]![
                                                              subcategory]!
                                                          .map((entry) {
                                                        return dataRow(category, entry);
                                                      }).toList()
                                                    ],
                                                  );
                                                }).toList(),
                                              ),
                                              SizedBox(
                                                height: 10,
                                              ),
                                            ],
                                          ),
                                        ),
                                      ],
                                    );
                                  }).toList(),
                                ),

                          Container(
                            decoration: BoxDecoration(
                                color: Color.fromRGBO(72, 155, 121, 0.1),
                                borderRadius: BorderRadius.circular(10)
                            ),
                            padding: EdgeInsets.all(10),
                            margin:  EdgeInsets.only(top: 10),
                            child: Row(
                              children: [
                                Expanded(
                                  child: NarFormLabelWidget(
                                    label: 'Sconta totale',
                                    fontSize: 18,
                                    textColor: Theme.of(context).primaryColor,
                                    fontWeight: 'bold',
                                  ),
                                ),
                                Expanded(
                                  flex: 2,
                                  child: Row(
                                    mainAxisAlignment: MainAxisAlignment.end,
                                    children: [
                                      Container(
                                        width: 168,
                                        child: NarSelectBoxWidget(
                                          options: discountTypes,
                                          controller: contDiscountType,
                                          contentPadding: EdgeInsets.only(
                                              top: 15,
                                              bottom: 17,
                                              left: 21.0,
                                              right: 8.0
                                          ),
                                          onChanged: (value){
                                            contDiscount!.text = '';
                                            setState(() {});
                                          },
                                        ),
                                      ),
                                      SizedBox(width: 10,),
                                      Container(
                                        width: 168,
                                        child:
                                        contDiscountType!.text == 'Percentuale'
                                            ? NarSelectBoxWidget(
                                          options: discounts,
                                          controller: contDiscount,
                                          contentPadding: EdgeInsets.only(
                                              top: 15,
                                              bottom: 17,
                                              left: 21.0,
                                              right: 8.0
                                          ),
                                          onChanged: (value){
                                            setState(() {
                                              widget.renovationQuotation?.discount = value;
                                            });
                                          },
                                        )
                                            : Row(
                                          children: [
                                            CustomTextFormField(

                                              controller: contDiscount,
                                              label: '',
                                              isMoney: true,
                                              onChangedCallback: (value) {
                                                String newVal = value.toString().replaceAll(".", "").replaceAll(",", ".");
                                                setState(() {
                                                  widget.renovationQuotation?.discount = newVal;
                                                });
                                              },
                                            ),
                                          ],
                                        ),
                                      ),
                                      SizedBox(width: 10,),
                                      BaseNewarcButton(
                                          buttonText: 'Applica',
                                          color: Theme.of(context).primaryColor,
                                          textColor: Colors.white,
                                          height: 41,
                                          onPressed: () async {
                                            subCategoryTotalCalc();
                                            setState(() {});
                                          }
                                      ),
                                      SizedBox(width: 10,),
                                      BaseNewarcButton(
                                          buttonText: 'Rimuovi',
                                          color: Color(0xFFE82525),
                                          textColor: Colors.white,
                                          height: 41,
                                          onPressed: () async {
                                            widget.renovationQuotation?.discount = "";
                                            contDiscount?.clear();
                                            subCategoryTotalCalc();
                                            setState(() {});
                                          }
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                          SizedBox(height: 15),
                          Container(
                            decoration: BoxDecoration(
                                color: Theme.of(context).primaryColor,
                                borderRadius: BorderRadius.circular(10)
                            ),
                            padding: EdgeInsets.all(15),
                            margin: EdgeInsets.only(top: 15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Totale',
                                  fontSize: 18,
                                  textColor: Color(0xffffffff),
                                  fontWeight: 'bold',
                                ),
                                Container(
                                  width: 250,
                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.end,
                                    children: [
                                      NarFormLabelWidget(
                                        label: '€ ${localCurrencyFormatMain.format(((){
                                          double quotTotal = grandTotal;
                                          return quotTotal;})())} + iva',
                                        textAlign: TextAlign.right,
                                        fontSize: 20,
                                        textColor: Color(0xffffffff),
                                        fontWeight: 'bold',
                                      ),
                                      NarFormLabelWidget(
                                        label: '€ ${localCurrencyFormatMain.format(((){
                                          double quotTotal = grandTotal;
                                          return quotTotal*1.1;})())} iva inclusa',
                                        textAlign: TextAlign.right,
                                        fontSize: 12,
                                        textColor: Color(0xffffffff),
                                        fontWeight: '600',
                                      ),

                                    ],
                                  ),
                                )
                              ],
                            ),
                          ),


                        ],
                      ),
                    ),
                    SizedBox(
                      height: 30,
                    ),


                    //-------- Pagamenti
                    Container(
                      padding: EdgeInsets.all(15),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisSize: MainAxisSize.max,
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            crossAxisAlignment: CrossAxisAlignment.center,
                            children: [
                              NarFormLabelWidget(
                                label: 'Pagamenti',
                                fontSize: 20,
                                fontWeight: 'bold',
                              ),
                              BaseNewarcButton(
                          buttonText: isCreatingPagamenti ? 'Riporto pagamenti...' : 'Riporta pagamenti',
                          width: 182,
                          height: 40,
                          fontWeight: "600",
                          color: Theme.of(context).primaryColor.withOpacity(isCreatingPagamenti ? 0.5 : 1),
                          textColor: Colors.white.withOpacity(isCreatingPagamenti ? 0.5 : 1),
                          fontSize: 15,
                          onPressed: isCreatingPagamenti ? (){} : () async {
                            setState(() {
                              isCreatingPagamenti = true;
                            });
                            try {
                              await saveQuotation(isFromPagamento: true);

                              List categories = await fetchCategory();

                              log("categories ===> ${categories}");

                              Map<String, List<String>> pagamentoToActivityMapCopy = Map.from(pagamentoToActivityMap);


                              List<NewarcProjectFixedAssetsPropertyPagamento> _pagamento = List.from(widget.renovationQuotation?.pagamento ?? []);

                              final Set<String> currentActivityCategories = widget.renovationQuotation?.renovationActivity?.map((e) => e.category).whereType<String>().toSet() ?? {};

                              final Set<String> validPagamentoLabels = pagamentoToActivityMapCopy.entries
                                  .where((entry) => entry.value.any((cat) => currentActivityCategories.contains(cat)))
                                  .map((entry) => entry.key)
                                  .toSet();


                              // ----- Remove  pagamento which is deleted from activity category
                              _pagamento.removeWhere((paga) {
                                return paga.categoryName != null && !validPagamentoLabels.contains(paga.categoryName.toString().trim());
                              });

                              // ----- Add missing pagamento entries
                              for (final entry in pagamentoToActivityMapCopy.entries) {
                                final String pagamentoLabel = entry.key;
                                final List<String> matchedActivityCats = entry.value;

                                final Map selectedCategory = categories.firstWhere(
                                      (cat) => cat['label'] == pagamentoLabel,
                                );

                                if (selectedCategory.isEmpty) continue;

                                final alreadyExists = _pagamento.any((p) =>
                                p.newarcProjectFixedAssetsPropertyCategoryId == selectedCategory['value']);

                                if (alreadyExists) continue;

                                double categoryTotal = 0;

                                for (final activityCategory in widget.renovationQuotation?.renovationActivity ?? []) {
                                  if (matchedActivityCats.contains(activityCategory.category.toString().trim())) {
                                    for (final activity in activityCategory.activity ?? []) {
                                      final double qty = activity.quantity ?? 0;
                                      final double price = activity.unitPrice ?? 0;
                                      final double discount = activity.activityDiscountAmount ?? 0;
                                      categoryTotal += (qty * price) - discount;
                                    }
                                  }
                                }

                                if (categoryTotal > 0) {
                                  final NewarcProjectFixedAssetsPropertyPagamento paga = NewarcProjectFixedAssetsPropertyPagamento.empty();
                                  paga.newarcProjectFixedAssetsPropertyCategoryId = selectedCategory['value'];
                                  paga.categoryName = selectedCategory['label'];

                                  if (selectedCategory['label'].toString().trim() == "Ristrutturazione") {
                                    if (widget.renovationQuotation?.discount?.isNotEmpty ?? false) {
                                      if (widget.renovationQuotation!.discount!.contains("%")) {
                                        double wholeQuotationDiscountDouble = double.tryParse(widget.renovationQuotation!.discount!.replaceAll("%", "")) ?? 0.0;
                                        double catValueWithDiscount = categoryTotal * wholeQuotationDiscountDouble / 100;
                                        paga.total = categoryTotal - catValueWithDiscount;
                                      } else {
                                        double lavoriCategoryTotal = 0;
                                        double wholeQuotationDiscountDouble = double.tryParse(widget.renovationQuotation!.discount!) ?? 0.0;
                                        for (final activityCategory in widget.renovationQuotation?.renovationActivity ?? []) {
                                          if (matchedActivityCats.contains(activityCategory.category)) {
                                            for (final activity in activityCategory.activity ?? []) {
                                              final double qty = activity.quantity ?? 0;
                                              final double price = activity.unitPrice ?? 0;
                                              final double discount = activity.activityDiscountAmount ?? 0;
                                              lavoriCategoryTotal += (qty * price) - discount;
                                            }
                                          }
                                        }
                                        paga.total = lavoriCategoryTotal - wholeQuotationDiscountDouble;
                                      }
                                    }
                                  } else {
                                    paga.total = categoryTotal;
                                  }

                                  _pagamento.add(paga);
                                }
                              }

                              widget.renovationQuotation?.pagamento = _pagamento;
                              setState(() {
                                isCreatingPagamenti = false;
                              });
                            } catch (e, s) {
                              setState(() {
                                isCreatingPagamenti = false;
                              });
                              log("Error While Creating Pagamento: $e");
                              log("Stacktrace: ${s.toString()}");
                            }
                          }
                      ),
                            ],
                          ),

                          isLoadingPagamenti ?
                          Center(
                              child: CircularProgressIndicator(color: Theme.of(context).primaryColor,),
                          ) :
                          Padding(
                            padding: const EdgeInsets.only(top: 30),
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisAlignment: MainAxisAlignment.start,
                              children: [
                                ...?widget.renovationQuotation?.pagamento?.asMap().entries.map((entry) {
                                final index = entry.key;
                                final pagamento = entry.value;
                                final rateList = pagamento.rate ?? [];
                                final total = pagamento.total ?? 0;
                                final isRateAdded = rateList.isNotEmpty;

                                return Container(
                                  width: double.infinity,
                                  margin: const EdgeInsets.only(bottom: 10),
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(8),
                                    border: Border.all(color: Color(0xFFE0E0E0)),
                                  ),

                                  child: Column(
                                    crossAxisAlignment: CrossAxisAlignment.start,
                                    mainAxisAlignment: MainAxisAlignment.start,
                                    children: [
                                      Container(
                                        width: double.infinity,
                                        height: 40,
                                        margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 15),
                                        child: Row(
                                          crossAxisAlignment: CrossAxisAlignment.center,
                                          mainAxisAlignment: MainAxisAlignment.start,
                                          children: [
                                            Expanded(
                                              flex: 1,
                                              child: Row(
                                                mainAxisAlignment: MainAxisAlignment.start,
                                                crossAxisAlignment: CrossAxisAlignment.center,
                                                children: [
                                                  Container(
                                                    height: 16,
                                                    width: 16,
                                                    decoration: BoxDecoration(
                                                      color: isRateAdded ? Theme.of(context).primaryColor : Color(0xffD1D1D1),
                                                      borderRadius: BorderRadius.circular(14),
                                                    ),
                                                    child: Icon(
                                                      isRateAdded ? Icons.check : Icons.close_rounded,
                                                      color:  Colors.white,
                                                      size: 16,
                                                    ),
                                                  ),
                                                  SizedBox(width: 5,),
                                                  NarFormLabelWidget(
                                                    label: pagamento.categoryName ?? "",
                                                    fontSize: 15,
                                                    fontWeight: '700',
                                                    textColor: AppColor.black,
                                                  ),
                                                ],
                                              ),
                                            ),
                                            Expanded(
                                              flex: 1,
                                              child: NarFormLabelWidget(
                                                label:
                                                "${localCurrencyFormatMain.format(total).toString().trim()}€+iva",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.black,
                                              ),
                                            ),
                                            isRateAdded
                                                ?
                                            Expanded(
                                              child: Align(
                                                alignment: Alignment.centerRight,
                                                child: BaseNewarcButton(
                                                  buttonText: 'Modifica',
                                                  fontSize: 14,
                                                  fontWeight: "600",
                                                  color: Color(0xFFD0D0D0),
                                                  textColor: AppColor.black,
                                                  height: 40,
                                                  width: 116,
                                                  onPressed: () async {
                                                    showAddPagamentiPopup(newNewarcProjectFixedAssetsPropertyPagamento: pagamento,index: index);
                                                  },
                                                ),
                                              ),
                                            )
                                                :
                                            Expanded(
                                              child: Align(
                                                alignment: Alignment.centerRight,
                                                child: BaseNewarcButton(
                                                  buttonText: 'Definisci pagamenti',
                                                  fontSize: 14,
                                                  fontWeight: "600",
                                                  height: 40,
                                                  width: 163,
                                                  onPressed: () async {
                                                    NewarcProjectFixedAssetsPropertyPagamento _paga = NewarcProjectFixedAssetsPropertyPagamento.empty();
                                                    _paga.categoryName = pagamento.categoryName;
                                                    _paga.newarcProjectFixedAssetsPropertyCategoryId = pagamento.newarcProjectFixedAssetsPropertyCategoryId;
                                                    _paga.total = pagamento.total;
                                                    showAddPagamentiPopup(newNewarcProjectFixedAssetsPropertyPagamento: _paga,index: index);
                                                  },
                                                ),
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                      // Rata Rows
                                      ...rateList.map((rate) {
                                        final rataLabel = "Rata ${rate.index} – ${rate.percentage ?? 0}%";
                                        final amount = rate.rate ?? 0;

                                        return Container(
                                          width: double.infinity,
                                          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
                                          child: Row(
                                            children: [
                                              Expanded(
                                                flex: 1,
                                                child: NarFormLabelWidget(
                                                  label: rataLabel,
                                                  fontSize: 12,
                                                  fontWeight: '500',
                                                  textColor: AppColor.black,
                                                ),
                                              ),
                                              Expanded(
                                                flex: 1,
                                                child: Container(
                                                  child: NarFormLabelWidget(
                                                    label: "${localCurrencyFormatMain.format(amount).toString().trim()}€+iva",
                                                    fontSize: 12,
                                                    fontWeight: '500',
                                                    textColor: AppColor.black,
                                                  ),
                                                ),
                                              ),
                                              Expanded(flex:1,child: NarFormLabelWidget(
                                                label: rate.description ?? "",
                                                fontSize: 12,
                                                fontWeight: '500',
                                                textColor: AppColor.black,
                                              )),
                                            ],
                                          ),
                                        );
                                      }).toList(),
                                      SizedBox(height: isRateAdded ? 10 : 0),
                                    ],
                                  ),
                                );
                              })],
                            ),
                          ),
                        ],
                      ),
                    ),

                    SizedBox(
                      height: 30,
                    ),

                    //------ Durata
                    Container(
                      padding: EdgeInsets.all(15),
                      decoration: BoxDecoration(
                          color: Colors.white,
                          borderRadius: BorderRadius.circular(13),
                          border: Border.all(width: 1, color: Color(0XFFB7B7B7))),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          NarFormLabelWidget(
                            label: 'Durata cantiere',
                            fontSize: 20,
                            fontWeight: 'bold',
                          ),
                          SizedBox(height: 12),
                          Container(
                            decoration: BoxDecoration(
                                color: Color.fromRGBO(72, 155, 121, 0.1),
                                borderRadius: BorderRadius.circular(10)),
                            padding: EdgeInsets.all(15),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.spaceBetween,
                              mainAxisSize: MainAxisSize.max,
                              crossAxisAlignment: CrossAxisAlignment.center,
                              children: [
                                NarFormLabelWidget(
                                  label: 'Durata presunta cantiere',
                                  fontSize: 15,
                                  textColor: Theme.of(context).primaryColor,
                                  fontWeight: 'bold',
                                ),
                                Container(
                                  width: 250,
                                  child: NarSelectBoxWidget(
                                    options: constructionDuration,
                                    controller: contConstructionDuration,
                                    validationType: 'required',
                                    parametersValidate: 'Required!',
                                    contentPadding: EdgeInsets.only(
                                        top: 15,
                                        bottom: 17,
                                        left: 21.0,
                                        right: 8.0),
                                  ),
                                )
                              ],
                            ),
                          ),
                        ],
                      ),
                    ),


                    //------Salva Button
                    SizedBox(
                      height: 5,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        NarFormLabelWidget(label: formMessages[0]),
                      ],
                    ),
                    SizedBox(
                      height: 5,
                    ),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.end,
                      children: [
                        BaseNewarcButton(
                            buttonText: 'Salva',
                            onPressed: () async {
                              await saveQuotation(isFromPagamento: false);
                            }
                        ),
                      ],
                    )
                  ],
                ),
              ),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> showAddPercentagePopup({required VoidCallback onPercentageAdded}) async {
    TextEditingController perController = TextEditingController();
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Aggiungi sottocategoria",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      User? user = FirebaseAuth.instance.currentUser;
                      if (user == null) return;

                      final String uid = user.uid;
                      final int per = int.tryParse(perController.text.trim()) ?? 0;
                      final collectionRef = FirebaseFirestore.instance
                          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE);

                      await collectionRef.add({
                        "percentage": per,
                        "insertTimestamp": DateTime.now().millisecondsSinceEpoch,
                        "uid": uid,
                      });

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                      onPercentageAdded();
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding percentage -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: CustomTextFormField(
                      isExpanded: false,
                      isNumber: true,
                      isPercentage: true,
                      label: "Digita il nuovo percentuale",
                      validator: (value) {
                        if (value == '') {
                          return 'Required!';
                        }
                        return null;
                      },
                      controller: perController,
                    ),
                  ),
                ));
          });
        });
  }

  Future<List> fetchPercentage() async {
    try {
      List<NewarcProjectFixedAssetsPropertyPercentage> _newarcProjectFixedAssetsPropertyPercentage = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcProjectFixedAssetsPropertyPercentage _tmp =
            NewarcProjectFixedAssetsPropertyPercentage.fromDocument(
                element.data(), element.id);
            _newarcProjectFixedAssetsPropertyPercentage.add(_tmp);
          } catch (e) {
            print("ERROR Percentage ---> $e");
          }
        }
      }


      if (_newarcProjectFixedAssetsPropertyPercentage.length > 0) {
        return _newarcProjectFixedAssetsPropertyPercentage.map((e) {
          return {'value': "${e.firebaseId}|${e.percentage}", 'label': "${e.percentage}%"};
        }).toList();
      } else {
        return [
          {'value': '', 'label': ''}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {'value': '', 'label': ''}
      ];
    }
  }

  Future<List> fetchCategory() async {
    try {
      List<NewarcProjectFixedAssetsPropertyCategory> _newarcProjectFixedAssetsPropertyCategory = [];
      QuerySnapshot<Map<String, dynamic>> collectionSnapshot;
      Query<Map<String, dynamic>> collectionSnapshotQuery;

      collectionSnapshotQuery = FirebaseFirestore.instance
          .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY);

      collectionSnapshot = await collectionSnapshotQuery
          .orderBy('insertTimestamp', descending: true)
          .get();

      if (collectionSnapshot.docs.length > 0) {
        for (var element in collectionSnapshot.docs) {
          try {
            NewarcProjectFixedAssetsPropertyCategory _tmp =
            NewarcProjectFixedAssetsPropertyCategory.fromDocument(
                element.data(), element.id);
            _newarcProjectFixedAssetsPropertyCategory.add(_tmp);
          } catch (e) {
            print("ERROR fetchCategory ---> $e");
          }
        }
      }


      if (_newarcProjectFixedAssetsPropertyCategory.length > 0) {
        return _newarcProjectFixedAssetsPropertyCategory.map((e) {
          return {'value': "${e.firebaseId}", 'label': e.name};
        }).toList();
      } else {
        return [
          {}
        ];
      }
    } catch (e, s) {
      print('error: $e');
      print(s);
      return [
        {}
      ];
    }
  }

  Future<void> fetchPagamento() async {
    List<NewarcProjectFixedAssetsPropertyPagamento>? _pagamento = List<NewarcProjectFixedAssetsPropertyPagamento>.from(widget.renovationQuotation?.pagamento ?? [],);

    setState(() {
      isLoadingPagamenti = true;
    });

    try {
      for (NewarcProjectFixedAssetsPropertyPagamento pagamento in _pagamento) {
        if (!(pagamento.isManualCategory ?? false)) {
          DocumentSnapshot<Map<String, dynamic>> collectionQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYCATEGORY)
              .doc(pagamento.newarcProjectFixedAssetsPropertyCategoryId)
              .get();

          if (collectionQuery.exists) {
            pagamento.categoryName = collectionQuery.data()?["name"];
          }
        }

        for (var rate in pagamento.rate ?? []) {
          DocumentSnapshot<Map<String, dynamic>> perQuery = await FirebaseFirestore.instance
              .collection(appConfig.COLLECT_NEWARCPROJECTFIXEDASSETSPROPERTYPERCENTAGE)
              .doc(rate.newarcProjectFixedAssetsPercentageId)
              .get();

          if (perQuery.exists) {
            rate.percentage = perQuery.data()?["percentage"];
          }
        }
      }

      widget.renovationQuotation?.pagamento = _pagamento;

    } catch (e, s) {
      print('Error while fetching Pagamento: $e');
      print(s);
    } finally {
      setState(() {
        isLoadingPagamenti = false;
      });
    }
  }

  void moveCategory(String category, bool moveUp) {
    int index = categoryOrder.indexOf(category);

    if ((moveUp && index > 0) || (!moveUp && index < categoryOrder.length - 1)) {
      setState(() {
        // Swap the category positions in categoryOrder
        String tempCategory = categoryOrder[index];
        categoryOrder[index] = categoryOrder[index + (moveUp ? -1 : 1)];
        categoryOrder[index + (moveUp ? -1 : 1)] = tempCategory;

        // Update the renovationData map to reflect the new order
        Map<String, Map<String, List<Map>>> newRenovationData = {};
        for (String cat in categoryOrder) {
          newRenovationData[cat] = renovationData[cat]!;
        }
        renovationData = newRenovationData;
      });
    }
  }

  void moveSubCategory(String category, String subCategory, bool moveUp) {
    // Get the current subcategory order
    List<String> subCategoryKeys = renovationData[category]!.keys.toList();
    int index = subCategoryKeys.indexOf(subCategory);

    if ((moveUp && index > 0) || (!moveUp && index < subCategoryKeys.length - 1)) {
      setState(() {
        // Swap subcategory keys
        String temp = subCategoryKeys[index];
        subCategoryKeys[index] = subCategoryKeys[index + (moveUp ? -1 : 1)];
        subCategoryKeys[index + (moveUp ? -1 : 1)] = temp;

        // Rebuild the inner map in new order
        Map<String, List<Map>> reorderedSubCategories = {};
        for (String key in subCategoryKeys) {
          reorderedSubCategories[key] = renovationData[category]![key]!;
        }

        renovationData[category] = reorderedSubCategories;
      });
    }
  }

 Future<void> saveQuotation({required bool isFromPagamento}) async {
    if (!(_formKey.currentState?.validate() ?? false)) {
      setState(() {
        formMessages.clear();
        formMessages
            .add('All values are required');
      });
      return;
    }

    setState(() {
      formMessages.clear();
      formMessages.add('Saving');
    });


    try {
      bool hasEmptyRates = widget.renovationQuotation?.pagamento
          ?.any((paga) => paga.rate?.isEmpty ?? true) ?? false;

      if(!isFromPagamento && hasEmptyRates){
        setState(() {
          formMessages.clear();
          formMessages
              .add('please fill rates');
        });
        return;
      }

      List<RenovationActivityCategory>
          renovationActivityCategory = [];

      int categoryCounter = 0;

      renovationData.forEach((_key, renovationDataSubCat) {
        List<RenovationActivity>
            renovationActivity = [];

        if (renovationDataSubCat.length > 0) {
          renovationDataSubCat.forEach((key, value) {
            int raCounter = 0;
            if (value.length > 0) {
              value.forEach((raValue) {
                RenovationActivity tmpRA = new RenovationActivity({
                  'index': raCounter++,
                  'title': raValue['title'],
                  'measurementUnit': raValue['measurementUnit'],
                  'quantity': double.tryParse(raValue['quantity'].toString()),
                  'unitPrice': double.tryParse(raValue['unitPrice'].toString().replaceAll(".", "").replaceAll(",", ".")),
                  'description':
                      raValue['description'],
                  'priceLevel':
                      raValue['priceLevel'],
                  'subCategory':
                      raValue['subCategory'],
                  'code': raValue['code'],
                  'comment': raValue['comment'],
                  'isDiscounted': raValue['isDiscounted'],
                  'isManualActivity': raValue['isManualActivity'],
                  'isIncludedInComputoMatric': raValue['isIncludedInComputoMatric'],
                  'activityDiscountAmount': raValue['activityDiscountAmount'],
                  'activityDiscount': raValue['activityDiscount'],
                });
                renovationActivity.add(tmpRA);
              });
            }
          });
        }
        RenovationActivityCategory tmpRAC =
            new RenovationActivityCategory({
          'index': categoryCounter++,
          'category': _key,
          'activity': renovationActivity
        });

        renovationActivityCategory.add(tmpRAC);
      });

      widget.renovationQuotation!.renovationActivity =  renovationActivityCategory;
      // widget.renovationQuotation!.paymentMode = contPaymentMode!.text;
      widget.renovationQuotation!.constructionDuration = contConstructionDuration!.text;

      widget.renovationQuotation!.discountType =  contDiscountType!.text;
      widget.renovationQuotation!.discount =  contDiscount!.text.replaceAll(".", "").replaceAll(",", ".");

      // set modification date
      widget.renovationQuotation!.modificationDate =  DateTime.now().millisecondsSinceEpoch;

      List<NewarcProjectFixedAssetsPropertyPagamento> pagamentoList = List.from(widget.renovationQuotation?.pagamento ?? []);

      Map<String, List<String>> pagamentoToActivityMapCopy = Map.from(pagamentoToActivityMap);

      for (var paga in pagamentoList) {
        final matchedActivityCats = pagamentoToActivityMapCopy[paga.categoryName.toString().trim()] ?? [];

        double categoryTotal = 0;

        for (final actCat in widget.renovationQuotation?.renovationActivity ?? []) {
          if (matchedActivityCats.contains(actCat.category.toString().trim())) {
            for (final act in actCat.activity ?? []) {
              final qty = act.quantity ?? 0;
              final price = act.unitPrice ?? 0;
              final discount = act.activityDiscountAmount ?? 0;
              categoryTotal += (qty * price) - discount;
            }
          }
        }

        //------ Apply discount only for "Ristrutturazione"
        if (paga.categoryName.toString().trim() == "Ristrutturazione") {
          final discountStr = widget.renovationQuotation?.discount;
          if (discountStr?.isNotEmpty ?? false) {
            if (discountStr!.contains("%")) {
              double discountPercent = double.tryParse(discountStr.replaceAll("%", "")) ?? 0.0;
              paga.total = categoryTotal * (1 - discountPercent / 100);
            } else {
              double discountFixed = double.tryParse(discountStr) ?? 0.0;
              paga.total = categoryTotal - discountFixed;
            }
          } else {
            paga.total = categoryTotal;
          }
        } else {
          paga.total = categoryTotal;
        }


        double totalIVA = 0.0;
        for (var rate in paga.rate ?? []) {
          if (rate.percentage != null) {
            double pct = rate.percentage! / 100;
            rate.rate = paga.total! * pct;
            double ivaPct = rate.ivaPercentage ?? 0;
            double ivaAmount = (rate.rate! * ivaPct) / 100;

            rate.ivaAmount = ivaAmount;
            totalIVA += ivaAmount;
          }
        }

        paga.totalIVA = totalIVA;
      }


      widget.renovationQuotation?.pagamento = pagamentoList;

      await FirebaseFirestore.instance
          .collection(appConfig
              .COLLECT_RENOVATION_QUOTATION)
          .doc(widget.renovationQuotation!.id)
          .update(widget.renovationQuotation!.toMap());

      setState(() {
        formMessages.clear();
        formMessages.add('Saved!');
      });

      changedDetected = false;
    } catch (e, s) {
      log("saveQuotation ERROR --------> ${e.toString()}");
      log("saveQuotation STACKTRACE --------> ${s.toString()}");
      print({e, s});
      setState(() {
        formMessages.clear();
        formMessages.add('Error occured');
      });
    }
  }

 Future<void> scontaVoceDialog({required String category,required String subCategory,required int rowIndex}) async {
    TextEditingController perController = TextEditingController();
    TextEditingController manualAmountController = TextEditingController();
    TextEditingController discountTypeController = TextEditingController(text: "Percentuale");
    List<String> formErrorMessage = [];
    await showDialog(
        context: context,
        builder: (BuildContext _context) {
          return StatefulBuilder(builder: (__context, _setState) {
            return Center(
                child: BaseNewarcPopup(
                  title: "Sconta voce",
                  buttonText: "Applica sconto",
                  formErrorMessage: formErrorMessage,
                  onPressed: () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {
                      double totalAmount = double.tryParse(renovationData[category]![subCategory]![rowIndex]['contTotal'].text.toString().replaceAll(".", "").replaceAll(",", ".")) ?? 0;

                      if(discountTypeController.text.trim() == "Percentuale"){



                        double discountPer = double.tryParse(perController.text.replaceAll("%", "").toString()) ?? 0 ;
                        renovationData[category]![subCategory]![rowIndex]['activityDiscount'] = perController.text;
                        if (discountPer != 0) {
                          double discountAmount = totalAmount * (discountPer / 100);
                          renovationData[category]![subCategory]![rowIndex]['activityDiscountAmount'] = discountAmount;
                        }
                      }else if(discountTypeController.text.trim() == "Manuale"){
                        if (manualAmountController.text.isNotEmpty) {
                          renovationData[category]![subCategory]![rowIndex]['activityDiscount'] = manualAmountController.text.replaceAll(".", "").replaceAll(",", ".");
                          double discountAmount = double.tryParse(manualAmountController.text.replaceAll(".", "").replaceAll(",", ".")) ?? 0;

                          renovationData[category]![subCategory]![rowIndex]['activityDiscountAmount'] = discountAmount;
                        }
                      }

                      renovationData[category]![subCategory]![rowIndex]['isDiscounted'] = true;


                      subCategoryTotalCalc();

                      setState(() {});
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding Scanto voca -----> ${e.toString()}");
                    }
                  },
                  buttonColor: Theme.of(context).primaryColor,
                  column: Container(
                    width: 400,
                    child: Column(
                      children: [
                        NarSelectBoxWidget(
                          label: "Seleziona tipologia",
                          options: ["Percentuale","Manuale"],
                          controller: discountTypeController,
                          onChanged: (value){
                            _setState(() {});
                          },
                        ),
                        SizedBox(height: 10,),

                        discountTypeController.text == "Percentuale" ?
                        NarSelectBoxWidget(
                          label: "Seleziona percentuale",
                          options: ["5%","10%","15%","20%","25%","100%"],
                          controller: perController,
                          validationType: 'required',
                          parametersValidate: 'Required!',
                        ) :
                        CustomTextFormField(
                          isExpanded: false,
                          isMoney: true,
                          label: "Inserisci amount",
                          validator: (value) {
                            if (value == '') {
                              return 'Required!';
                            }
                            return null;
                          },
                          controller: manualAmountController,
                        ),
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  Future<void> showAddPagamentiPopup({NewarcProjectFixedAssetsPropertyPagamento? newNewarcProjectFixedAssetsPropertyPagamento,int? index}) async {
    List<String> formErrorMessage = [];

    var _newarcProjectFixedAssetsPropertyPagamento = newNewarcProjectFixedAssetsPropertyPagamento != null
        ? NewarcProjectFixedAssetsPropertyPagamento.fromDocument(newNewarcProjectFixedAssetsPropertyPagamento.toMap(),"")
        : NewarcProjectFixedAssetsPropertyPagamento.empty();
    if(newNewarcProjectFixedAssetsPropertyPagamento?.categoryName != null){
      _newarcProjectFixedAssetsPropertyPagamento.categoryName = newNewarcProjectFixedAssetsPropertyPagamento?.categoryName;
    }

    TextEditingController manualCategoryController = TextEditingController(text: _newarcProjectFixedAssetsPropertyPagamento.isManualCategory ?? false ? _newarcProjectFixedAssetsPropertyPagamento.categoryName ?? "" : "");

    List<Rate> rateList = (newNewarcProjectFixedAssetsPropertyPagamento?.rate ?? [])
        .map((rate){
      Rate newRate = Rate.fromDocument(rate.toMap(),"");
      newRate.percentage = rate.percentage;
      return newRate;
    }).toList();

    bool isSaveButtonEnableFlag = false;

    bool isSaveButtonEnabled() {
      int totalPercentage = 0;
      for (var rate in rateList) {
        totalPercentage += rate.percentage ?? 0;
      }
      return totalPercentage == 100;
    }

    Future<List>? _fetchPercentageFuture;

    await showDialog(
        context: context,
        builder: (BuildContext _context) {

          return StatefulBuilder(builder: (__context, _setState) {

            _fetchPercentageFuture ??= fetchPercentage();

            void refreshFetchPercentage() {
              _setState(() {
                _fetchPercentageFuture = fetchPercentage();
              });
            }
            return Center(
                child: BaseNewarcPopup(
                  title: "Definisci pagamenti",
                  buttonText: "Aggiungi",
                  formErrorMessage: formErrorMessage,
                  onPressed: (isSaveButtonEnableFlag) ? () async {
                    _setState((){
                      formErrorMessage.clear();
                      formErrorMessage.add("Salvataggio in corso...");
                    });
                    try {

                      double totalIVA = 0.0;
                      for (var rate in rateList) {
                        totalIVA += rate.ivaAmount ?? 0.0;
                        if((rate.isMerged ?? false) && (rate.mergedIntoRateId?.isNotEmpty ?? false)){
                          rate.isMerged = true;


                          final targetRate = widget.renovationQuotation!.pagamento!
                              .expand((element) => element.rate ?? [])
                              .firstWhere(
                                (r) {
                              return r.uniqueId == rate.mergedIntoRateId;
                            },
                            orElse: () => Rate.empty(),
                          );

                          if (targetRate.uniqueId?.isNotEmpty ?? false) {
                            targetRate.margeRateUniqueIDS ??= [];

                            final index = targetRate.margeRateUniqueIDS!.indexWhere((id) => id == rate.uniqueId);

                            if (index != -1) {
                              // Already exists: update the value
                              targetRate.margeRateUniqueIDS![index] = rate.uniqueId;
                            } else {
                              // Doesn't exist: add it
                              targetRate.margeRateUniqueIDS!.add(rate.uniqueId);
                            }
                          }
                        }else{
                          rate.isMerged = false;
                          rate.mergedIntoRateId = "";
                        }
                      }
                      _newarcProjectFixedAssetsPropertyPagamento.totalIVA = totalIVA;
                      _newarcProjectFixedAssetsPropertyPagamento.rate = rateList;

                      if(_newarcProjectFixedAssetsPropertyPagamento.isManualCategory ?? false){
                        _newarcProjectFixedAssetsPropertyPagamento.categoryName = manualCategoryController.text.trim();
                      }

                      setState(() {
                        if (index != null && index >= 0 && widget.renovationQuotation!.pagamento != null && index < (widget.renovationQuotation!.pagamento?.length ?? 0)) {
                          widget.renovationQuotation!.pagamento?[index] = _newarcProjectFixedAssetsPropertyPagamento;
                        }
                      });

                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Salvato");
                      });
                    } catch (e) {
                      _setState((){
                        formErrorMessage.clear();
                        formErrorMessage.add("Si è verificato un errore.");
                      });
                      log("Error While Adding PAGAMENTO -----> ${e.toString()}");
                    }
                  } : (){return false;},
                  onPressedSecondButton: () {
                    WidgetsBinding.instance.addPostFrameCallback((_){
                      showDialog(
                        context: context,
                        barrierDismissible: true,
                        builder: (BuildContext context) {
                          return StatefulBuilder(builder: (context,setStateDialog){
                            return Center(
                              child: BaseNewarcPopup(
                                  buttonColor: Color(0xFFE82525),
                                  title: "Elimina Entrata",
                                  buttonText: "Rimuovi",
                                  onPressed: () async {
                                    try{
                                      if (index != null && index >= 0) {
                                        final sectionToRemove = widget.renovationQuotation!.pagamento![index];

                                        for (final rate in sectionToRemove.rate ?? []) {
                                          // Step 1: If this rate is merged into another, remove its reference from the parent
                                          if (rate.mergedIntoRateId?.isNotEmpty ?? false) {
                                            final parentRate = widget.renovationQuotation!.pagamento!
                                                .expand((e) => e.rate ?? [])
                                                .firstWhere(
                                                  (r) => r.uniqueId == rate.mergedIntoRateId,
                                              orElse: () => Rate.empty(),
                                            );

                                            if ((parentRate.uniqueId?.isNotEmpty ?? false) &&
                                                (parentRate.margeRateUniqueIDS?.contains(rate.uniqueId) ?? false)) {
                                              parentRate.margeRateUniqueIDS?.remove(rate.uniqueId);
                                              if (parentRate.margeRateUniqueIDS?.isEmpty ?? true) {
                                                parentRate.isMerged = false;
                                              }
                                            }
                                          }

                                          // Step 2: If this rate is a parent, unmerge all child rates
                                          final childRates = widget.renovationQuotation!.pagamento!
                                              .expand((e) => e.rate ?? [])
                                              .where((r) => r.mergedIntoRateId == rate.uniqueId)
                                              .toList();

                                          for (final child in childRates) {
                                            child.mergedIntoRateId = "";
                                            child.isMerged = false;
                                          }
                                        }

                                        // Step 3: Remove the section
                                        widget.renovationQuotation!.pagamento!.removeAt(index);
                                        _newarcProjectFixedAssetsPropertyPagamento = NewarcProjectFixedAssetsPropertyPagamento.empty();
                                        rateList.clear();
                                        _setState(() {});
                                        setState(() {});
                                      } else {
                                        _newarcProjectFixedAssetsPropertyPagamento = NewarcProjectFixedAssetsPropertyPagamento.empty();
                                        rateList.clear();
                                        _setState(() {});
                                        return false;
                                      }
                                    }catch(e,s){
                                      print("---------- ERROR While Elimina Entrata ------> ${e.toString()}");
                                    }
                                  },
                                  column: Container(
                                    width: 400,
                                    padding: EdgeInsets.symmetric(vertical: 25),
                                    child: Column(
                                      mainAxisAlignment: MainAxisAlignment.center,
                                      crossAxisAlignment: CrossAxisAlignment.center,
                                      children: [
                                        Center(
                                          child: NarFormLabelWidget(
                                            label:  "Vuoi davvero eliminare questa entrata?" ,
                                            fontSize: 18,
                                            fontWeight: '700',
                                          ),
                                        ),
                                        SizedBox(height: 10,),
                                        Center(
                                          child: NarFormLabelWidget(
                                            label:  "Assicurati prima che non sia già stata pagata!" ,
                                            fontSize: 18,
                                            fontWeight: '500',
                                          ),
                                        ),
                                      ],
                                    ),
                                  )),
                            );
                          });
                        },
                      );
                    });
                    return false;
                  },
                  disableButton: (!isSaveButtonEnableFlag),
                  buttonColor: Theme.of(context).primaryColor.withOpacity(isSaveButtonEnableFlag ? 1.0 : 0.5),
                  isSecondButtonVisible: true,
                  secondButtonColor: Color(0xFFEA3132),
                  secondButtonText: "Elimina",
                  column: Container(
                    height: MediaQuery.of(context).size.height * 0.80,
                    width: 800,
                    child: ListView(
                      shrinkWrap: true,
                      children: [
                        NarFormLabelWidget(
                          label: "${_newarcProjectFixedAssetsPropertyPagamento.categoryName}: ${localCurrencyFormatMain.format(_newarcProjectFixedAssetsPropertyPagamento.total).toString().trim()}€+iva",
                          fontSize: 14,
                          fontWeight: '600',
                          textColor: AppColor.black,
                        ),
                        SizedBox(height: 15,),
                        ...rateList.map((rate){
                          return Container(
                            decoration: BoxDecoration(
                                border: Border.all(color: Color(0xFFDBDBDB),width: 1),
                                borderRadius: BorderRadius.circular(7)
                            ),
                            padding: EdgeInsets.all(10),
                            margin: EdgeInsets.only(bottom: 15),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              crossAxisAlignment: CrossAxisAlignment.start,
                              children: [
                                NarFormLabelWidget(
                                  label: "Rate ${rate.index}",
                                  fontSize: 14,
                                  fontWeight: '700',
                                  textColor: AppColor.black,
                                ),
                                SizedBox(height: 10,),
                                Row(
                                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                  children: [
                                    //----Per
                                    Column(
                                      children: [
                                        SizedBox(
                                          width: 300,
                                          child: Row(
                                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                                            children: [
                                              NarFormLabelWidget(
                                                label: "Percentuale",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                              NarLinkWidget(
                                                fontSize: 12,
                                                fontWeight: '600',
                                                textColor: Theme.of(context).primaryColor,
                                                text: 'Nuova percentuale',
                                                textDecoration: TextDecoration.underline,
                                                onClick: () {
                                                  showAddPercentagePopup(onPercentageAdded: refreshFetchPercentage);
                                                },
                                              )
                                            ],
                                          ),
                                        ),
                                        SizedBox(height: 10),
                                        SizedBox(
                                          width: 300,
                                          child: FutureBuilder<List>(
                                              future: _fetchPercentageFuture,
                                              builder: (context, snapshot) {
                                                if (snapshot.hasData) {
                                                  return DropdownButtonFormField<String>(
                                                    isExpanded: true,
                                                    value: rate.newarcProjectFixedAssetsPercentageId?.isNotEmpty ?? false ? "${rate.newarcProjectFixedAssetsPercentageId}|${rate.percentage}" : null,
                                                    icon: Padding(
                                                      padding: EdgeInsets.only(right: 8),
                                                      child: SvgPicture.asset(
                                                        'assets/icons/arrow_down.svg',
                                                        width: 12,
                                                        color: Color(0xff7e7e7e),
                                                      ),
                                                    ),
                                                    style: TextStyle(
                                                      overflow: TextOverflow.ellipsis,
                                                      color: Colors.black,
                                                      fontSize: 12.0,
                                                      fontWeight: FontWeight.bold,
                                                      fontStyle: FontStyle.normal,
                                                    ),
                                                    borderRadius: BorderRadius.circular(8),
                                                    decoration: InputDecoration(
                                                      border: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      hintStyle: TextStyle(
                                                        color: Colors.grey,
                                                        fontSize: 15.0,
                                                        fontWeight: FontWeight.w800,
                                                        fontStyle: FontStyle.normal,
                                                        letterSpacing: 0,
                                                      ),
                                                      focusedBorder: OutlineInputBorder(
                                                        borderRadius:
                                                        BorderRadius.all(Radius.circular(8)),
                                                        borderSide: BorderSide(
                                                          color: Color.fromRGBO(227, 227, 227, 1),
                                                          width: 1,
                                                        ),
                                                      ),
                                                      contentPadding: EdgeInsets.symmetric(
                                                          horizontal: 12, vertical: 8),
                                                      fillColor: Colors.white,
                                                    ),
                                                    onChanged: (String? value) {
                                                      if (value != null && value.contains('|')) {
                                                        final parts = value.split('|');
                                                        final percentageId = parts[0];
                                                        final per = int.tryParse(parts[1]) ?? 0;

                                                        int rateIndex = rate.index! - 1;

                                                        double baseRate = (per * (_newarcProjectFixedAssetsPropertyPagamento.total ?? 0)) / 100;
                                                        double newRate = baseRate;

                                                        int iva = rate.ivaPercentage ?? 0;
                                                        double ivaAmount = (baseRate * iva) / 100;

                                                        //rate.ivaAmount = ivaAmount;


                                                        _setState(() {
                                                          rateList[rateIndex].percentage = per;
                                                          rateList[rateIndex].newarcProjectFixedAssetsPercentageId = percentageId;
                                                          rateList[rateIndex].rate = newRate;
                                                          rateList[rateIndex].uniqueId = generateRandomString(20);
                                                          rateList[rateIndex].ivaAmount = ivaAmount;

                                                          isSaveButtonEnableFlag = isSaveButtonEnabled();
                                                        });
                                                      }
                                                    },
                                                    dropdownColor: Colors.white,
                                                    items: snapshot.data?.map<DropdownMenuItem<String>>((item) {
                                                      return DropdownMenuItem<String>(
                                                        value: item['value'],
                                                        child: NarFormLabelWidget(
                                                          label: item['label']!,
                                                          textColor: Colors.black,
                                                          fontSize: 14,
                                                          fontWeight: '600',
                                                        ),
                                                      );
                                                    }).toList(),
                                                  );
                                                } else if (snapshot.hasError) {
                                                  return Container(
                                                    width: 30,
                                                    height: 30,
                                                    alignment: Alignment.center,
                                                  );
                                                }
                                                return Center(
                                                  child: CircularProgressIndicator(
                                                    color: Theme.of(context).primaryColor,
                                                  ),
                                                );
                                              }),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(width: 20,),

                                    //----Rate
                                    Column(
                                      crossAxisAlignment: CrossAxisAlignment.start,
                                      children: [
                                        Container(
                                          height: 38,
                                          child: NarFormLabelWidget(
                                            label: "Rata",
                                            fontSize: 14,
                                            fontWeight: '600',
                                            textColor: AppColor.greyColor,
                                          ),
                                          alignment: Alignment.center,
                                        ),
                                        SizedBox(
                                          width: 136,
                                          child: CustomTextFormField(
                                            label: "",
                                            enabled: false,
                                            isShowPrefillMoneyIcon: false,
                                            fillColor: AppColor.disabledGreyColor,
                                            isExpanded: false,
                                            isMoney: true,
                                            validator: (value) {
                                              if (value == '') {
                                                return 'Required!';
                                              }
                                              return null;
                                            },
                                            controller: TextEditingController(text: localCurrencyFormatMain.format(rate.rate)),
                                          ),
                                        ),
                                      ],
                                    ),
                                    // SizedBox(width: 10,),

                                    //---IVA
                                    Row(
                                      children: [
                                        Column(
                                          crossAxisAlignment: CrossAxisAlignment.start,
                                          children: [
                                            Container(
                                              height: 38,
                                              child: NarFormLabelWidget(
                                                label: "Iva",
                                                fontSize: 14,
                                                fontWeight: '600',
                                                textColor: AppColor.greyColor,
                                              ),
                                              alignment: Alignment.center,
                                            ),
                                            SizedBox(
                                              width:136,
                                              child: DropdownButtonFormField<int>(
                                                isExpanded: true,
                                                value: rate.ivaPercentage ?? 0,
                                                icon: Padding(
                                                  padding: EdgeInsets.only(right: 8),
                                                  child: SvgPicture.asset(
                                                    'assets/icons/arrow_down.svg',
                                                    width: 12,
                                                    color: Color(0xff7e7e7e),
                                                  ),
                                                ),
                                                style: TextStyle(
                                                  overflow: TextOverflow.ellipsis,
                                                  color: Colors.black,
                                                  fontSize: 12.0,
                                                  fontWeight: FontWeight.bold,
                                                  fontStyle: FontStyle.normal,
                                                ),
                                                borderRadius: BorderRadius.circular(8),
                                                decoration: InputDecoration(
                                                  border: OutlineInputBorder(
                                                    borderRadius:
                                                    BorderRadius.all(Radius.circular(8)),
                                                    borderSide: BorderSide(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  hintStyle: TextStyle(
                                                    color: Colors.grey,
                                                    fontSize: 15.0,
                                                    fontWeight: FontWeight.w800,
                                                    fontStyle: FontStyle.normal,
                                                    letterSpacing: 0,
                                                  ),
                                                  focusedBorder: OutlineInputBorder(
                                                    borderRadius:
                                                    BorderRadius.all(Radius.circular(8)),
                                                    borderSide: BorderSide(
                                                      color: Color.fromRGBO(227, 227, 227, 1),
                                                      width: 1,
                                                    ),
                                                  ),
                                                  contentPadding: EdgeInsets.symmetric(
                                                      horizontal: 12, vertical: 8),
                                                  fillColor: Colors.white,
                                                ),
                                                onChanged: (int? value) {
                                                  _setState(() {
                                                    rate.ivaPercentage = value ?? 0;

                                                    int per = rate.percentage ?? 0;
                                                    double baseRate = (per * (_newarcProjectFixedAssetsPropertyPagamento.total ?? 0)) / 100;

                                                    int iva = rate.ivaPercentage ?? 0;
                                                    double ivaAmount = (baseRate * iva) / 100;

                                                    rate.ivaAmount = ivaAmount;
                                                  });
                                                },
                                                validator: (value) {
                                                  if (value == null) {
                                                    return "Required!";
                                                  }
                                                  return null;
                                                },
                                                dropdownColor: Colors.white,
                                                items: <int>[0,10,22].map<DropdownMenuItem<int>>((item) {
                                                  return DropdownMenuItem<int>(
                                                    value: item,
                                                    child: NarFormLabelWidget(
                                                      label: item.toString(),
                                                      textColor: Colors.black,
                                                      fontSize: 14,
                                                      fontWeight: '600',
                                                    ),
                                                  );
                                                }).toList(),
                                              ),
                                            ),
                                          ],
                                        ),
                                        SizedBox(width: 10,),
                                        IconButtonWidget(
                                          onTap: () {
                                            showDialog(
                                              context: context,
                                              barrierDismissible: true,
                                              builder: (BuildContext context) {
                                                return StatefulBuilder(builder: (context,setStateDialog){
                                                  return Center(
                                                    child: BaseNewarcPopup(
                                                        buttonColor: Color(0xFFE82525),
                                                        title: "Elimina Entrata",
                                                        buttonText: "Rimuovi",
                                                        onPressed: () async {
                                                          try{

                                                            // 1. If this rate is merged into another, remove it from that parent
                                                            if (rate.mergedIntoRateId?.isNotEmpty ?? false) {
                                                              final parentRate = widget.renovationQuotation!.pagamento!
                                                                  .expand((element) => element.rate ?? [])
                                                                  .firstWhere(
                                                                    (r) => r.uniqueId == rate.mergedIntoRateId,
                                                                orElse: () => Rate.empty(),
                                                              );

                                                              if ((parentRate.uniqueId?.isNotEmpty ?? false) &&
                                                                  (parentRate.margeRateUniqueIDS?.contains(rate.uniqueId) ?? false)) {
                                                                parentRate.margeRateUniqueIDS?.remove(rate.uniqueId);

                                                                if (parentRate.margeRateUniqueIDS?.isEmpty ?? true) {
                                                                  parentRate.isMerged = false;
                                                                }
                                                              }
                                                            }

                                                            // 2. If this rate is a parent (others are merged into it), unmerge them
                                                            final childRates = widget.renovationQuotation!.pagamento!
                                                                .expand((element) => element.rate ?? [])
                                                                .where((r){
                                                              return r.mergedIntoRateId == rate.uniqueId;
                                                            }).toList();

                                                            for (final child in childRates) {
                                                              child.mergedIntoRateId = "";
                                                              child.isMerged = false;
                                                            }

                                                            // 3. Remove the rate itself
                                                            rateList.remove(rate);

                                                            // 4. Reassign indexes
                                                            for (int i = 0; i < rateList.length; i++) {
                                                              rateList[i].index = i + 1;
                                                            }

                                                            // 5. Refresh UI
                                                            _setState(() {});
                                                          }catch(e,s){
                                                            print("---------- ERROR While Elimina Entrata------> ${e.toString()}");
                                                          }
                                                        },
                                                        column: Container(
                                                          width: 400,
                                                          padding: EdgeInsets.symmetric(vertical: 25),
                                                          child: Column(
                                                            mainAxisAlignment: MainAxisAlignment.center,
                                                            crossAxisAlignment: CrossAxisAlignment.center,
                                                            children: [
                                                              Center(
                                                                child: NarFormLabelWidget(
                                                                  label:  "Vuoi davvero eliminare questa entrata?" ,
                                                                  fontSize: 18,
                                                                  fontWeight: '700',
                                                                ),
                                                              ),
                                                              SizedBox(height: 10,),
                                                              Center(
                                                                child: NarFormLabelWidget(
                                                                  label:  "Assicurati prima che non sia già stata pagata!" ,
                                                                  fontSize: 18,
                                                                  fontWeight: '500',
                                                                ),
                                                              ),
                                                            ],
                                                          ),
                                                        )),
                                                  );
                                                });
                                              },
                                            );
                                          },
                                          isSvgIcon: true,
                                          backgroundColor: Colors.transparent,
                                          icon: 'assets/icons/delete.svg',
                                          iconColor: AppColor.greyColor,
                                        ),
                                      ],
                                    )
                                  ],
                                ),
                                SizedBox(height: 15,),
                                CustomTextFormField(
                                  isExpanded: false,
                                  minLines: 1,
                                  label: "Descrizione",
                                  controller: rate.descriptionController,
                                  hintText: "Es. Posa impianti completata",
                                  onChangedCallback: (value){
                                    if(value.toString().trim().isNotEmpty){
                                      _setState(() {
                                        rate.description = value;
                                        isSaveButtonEnableFlag = isSaveButtonEnabled();
                                      });
                                    }
                                  },
                                ),
                              ],),
                          );
                        }),
                        Align(
                          alignment: Alignment.bottomCenter,
                          child: NarLinkWidget(
                            fontSize: 14,
                            fontWeight: '600',
                            textColor: Theme.of(context).primaryColor,
                            text: '+ Aggiungi rata',
                            textDecoration: TextDecoration.underline,
                            onClick: () {
                              int newIndex = rateList.length + 1;
                              Rate newRate = Rate.empty();
                              newRate.index = newIndex;
                              newRate.isPaid = false;
                              newRate.paidDate = null;
                              rateList.add(newRate);
                              _setState(() {});
                            },
                          ),
                        )
                      ],
                    ),
                  ),
                ));
          });
        });
  }

  void showSelectCategoryForPreventivoDialog({required BuildContext context,required Map<String, bool> categoryMap,required RenovationQuotation selectedRenovationQuotation}) async {
    return showDialog(
        context: context,
        barrierDismissible: true,
        builder: (BuildContext context) {
          return StatefulBuilder(builder: (context,setStateDialog){
            return Center(
              child: BaseNewarcPopup(
                  key: ValueKey("Scarica Preventivo"),
                  buttonColor: Theme.of(context).primaryColor,
                  title: "Scarica Preventivo",
                  buttonText: "Scarica",
                  onPressed: () async {
                    _showDownloadingDialog(context);
                    try {
                      await saveQuotation(isFromPagamento: false);
                      await downloadRenovationQuotationPDF(row: selectedRenovationQuotation,categoryMap: categoryMap);
                    }catch(e){
                      log("Error while download renovation quotation PDF ===> ${e.toString()}");
                    } finally {
                      Future.delayed(Duration(milliseconds: 200), () {
                        Navigator.of(context, rootNavigator: true).pop();
                      });
                    }
                  },
                  column: SizedBox(
                    width: 800,
                    height: MediaQuery.of(context).size.height * .50,
                    child:  Container(
                      width: 600,
                      decoration: BoxDecoration(
                          borderRadius: BorderRadius.circular(7),
                          border: Border.all(color: Color(0xFFA4A4A4),width: 1)
                      ),
                      child: Container(
                        height: MediaQuery.of(context).size.height * .45,
                        padding: EdgeInsets.symmetric(vertical: 25),
                        child: ListView(
                          shrinkWrap: true,
                          children: [
                            Padding(
                              padding: const EdgeInsets.only(left: 10),
                              child: NarFormLabelWidget(
                                label: "Seleziona tiplogia preventivo",
                                fontSize: 14,
                                fontWeight: "700",
                              ),
                            ),
                            SizedBox(height: 10,),
                            SizedBox(
                              width: 80,
                              child: NarCheckboxWidget(
                                label: "",
                                values: categoryMap,
                                singleSelected: true,
                                columns: 1,
                                fontSize: 13,
                                listTileBoxWidthHeight: 20,
                                childAspectRatio: 18,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )),
            );
          });
        });
  }

  Future<void> downloadRenovationQuotationPDF({required RenovationQuotation row,required Map<String, bool> categoryMap})async{
      Map<String, Map<String, List<Map>>> renovationData = {};
      Map<String, double> categoryTotal = {};
      Map<String, Map<String, double>> subCategoryTotal = {};
      Map<String, Map<String, Map<String, dynamic>>> subCategoryTotalForOnlyToShow = {};
      Map<String, Map<String, dynamic>> categoryTotalForOnlyToShow = {};


      String? selectedPagamentoCategory = categoryMap.entries.firstWhere((e) => e.value == true).key;



      if (selectedPagamentoCategory.isEmpty) return;


      // Only filter if it's not "Completo"
      bool skipFiltering = selectedPagamentoCategory == "Completo";
      List<String>? allowedActivityCategories = skipFiltering
          ? null
          : pagamentoToActivityMap[selectedPagamentoCategory];

      if (row.renovationActivity != null) {
        for (var i = 0; i < row.renovationActivity!.length; i++) {
          RenovationActivityCategory tmpRenovationActivityCategory = row.renovationActivity![i];
          String category = tmpRenovationActivityCategory.category!;

          if (!skipFiltering && (allowedActivityCategories == null || !allowedActivityCategories.contains(category))) {
            continue; // filter out if not in allowed list
          }

          if (!renovationData.containsKey(category)) {
            renovationData[category] = {};
          }
          if (!categoryTotal.containsKey(category)) {
            categoryTotal[category] = 0;
          }
          if (!categoryTotalForOnlyToShow.containsKey(category)) {
            categoryTotalForOnlyToShow[category] = {
              "catTotal": 0.0,
              "catTotalForPdf": 0.0,
              "isDiscounted": false,
              "catDiscountedAmount": 0.0,
            };
          }

          double catTotal = 0;
          double catTotalForPdf = 0;
          bool isCategoryDiscounted = false;

          for (var j = 0; j < tmpRenovationActivityCategory.activity!.length; j++) {
            RenovationActivity activity = tmpRenovationActivityCategory.activity![j];
            double total = (activity.quantity ?? 0) * (activity.unitPrice ?? 0);
            String subCategory = activity.subCategory!;

            Map<String, dynamic> rowData = {
              'index': activity.index,
              'title': activity.title,
              'measurementUnit': activity.measurementUnit,
              'quantity': activity.quantity,
              'unitPrice': localCurrencyFormatMain.format(activity.unitPrice).trim(),
              'description': activity.description,
              'priceLevel': activity.priceLevel,
              'subCategory': activity.subCategory,
              'code': activity.code,
              'comment': activity.comment,
              'isDiscounted': activity.isDiscounted,
              'isManualActivity': activity.isManualActivity,
              'activityDiscountAmount': activity.activityDiscountAmount,
              'total': 0.0,
            };

            if (!renovationData[category]!.containsKey(subCategory)) {
              renovationData[category]![subCategory] = [];
            }
            if (!subCategoryTotal.containsKey(category)) {
              subCategoryTotal[category] = {};
            }
            if (!subCategoryTotal[category]!.containsKey(subCategory)) {
              subCategoryTotal[category]![subCategory] = 0;
            }
            if (!subCategoryTotalForOnlyToShow.containsKey(category)) {
              subCategoryTotalForOnlyToShow[category] = {};
            }
            if (!subCategoryTotalForOnlyToShow[category]!.containsKey(subCategory)) {
              subCategoryTotalForOnlyToShow[category]![subCategory] = {
                "subCategoryTotal": 0.0,
                "subCategoryTotalWithoutDiscount": 0.0,
                "isDiscounted": false,
              };
            }

            bool isSubCategoryDiscounted = false;

            if (!activity.isDiscounted!) {
              subCategoryTotal[category]?[subCategory] = (subCategoryTotal[category]?[subCategory] ?? 0) + total;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += total;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotalWithoutDiscount"] += total;
              rowData['total'] = total;
              catTotal += total;
              catTotalForPdf += total;
            } else {
              double activityDiscountAmount = (double.tryParse( rowData['activityDiscountAmount'].toString()) ?? 0.0);
              if(![0.0,0].contains(activityDiscountAmount)){
                isSubCategoryDiscounted = true;
              }
              double totalWithDiscount = total - activityDiscountAmount;
              rowData['total'] = total;
              catTotal += totalWithDiscount;
              catTotalForPdf += total;
              subCategoryTotal[category]?[subCategory] = (subCategoryTotal[category]?[subCategory] ?? 0) + total;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotal"] += totalWithDiscount;
              subCategoryTotalForOnlyToShow[category]![subCategory]!["subCategoryTotalWithoutDiscount"] += total;
              isSubCategoryDiscounted = true;
            }

            // If any entry in this subcategory was discounted, mark the whole subcategory as discounted
            if (isSubCategoryDiscounted) {
              subCategoryTotalForOnlyToShow[category]![subCategory]!["isDiscounted"] = true;
              isCategoryDiscounted = true;
            }

            renovationData[category]![subCategory]!.add(rowData);
          }

          categoryTotal[category] = catTotal;

          double catDiscountedAmount = catTotal;
          if((row.discount?.isNotEmpty ?? false) && row.discount!.contains("%")){
            double wholeQuotationDiscountDouble = double.tryParse(row.discount!.replaceAll("%", "")) ?? 0.0;
            double catValueWithDiscount = catTotal * wholeQuotationDiscountDouble / 100;
            catDiscountedAmount = catTotal - catValueWithDiscount;
          }
          categoryTotalForOnlyToShow[category] = {
            "catTotal": catTotal,
            "catTotalForPdf": catTotalForPdf,
            "isDiscounted": isCategoryDiscounted,
            "catDiscountedAmount": catDiscountedAmount ?? 0.0,
          };
        }
      }
      await pdfDesign(row, renovationData, categoryTotal, subCategoryTotal,categoryTotalForOnlyToShow, subCategoryTotalForOnlyToShow,selectedPagamentoCategory.toString().trim());

  }

  void _showDownloadingDialog(BuildContext context) {
    showDialog(
      context: context,
      barrierDismissible: false,
      barrierColor: Colors.black54,
      builder: (_) {
        return Scaffold(
          backgroundColor: Colors.transparent,
          body: Center(
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              decoration: BoxDecoration(
                //color: Colors.black87,
                borderRadius: BorderRadius.circular(30),
              ),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  CircularProgressIndicator(color: AppColor.white,),
                  SizedBox(height: 10,),
                  NarFormLabelWidget(
                    textAlign: TextAlign.center,
                    label: "Creando PDF...",
                    fontSize: 18,
                    fontWeight: '700',
                    textColor: AppColor.white,
                  ),
                ],
              ),
            ),
          ),
        );
      },
    );
  }

}
