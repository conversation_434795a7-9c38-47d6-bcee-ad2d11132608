import 'package:flutter/material.dart';
import 'package:newarc_platform/pages/agency/models/home_agency.model.dart';
import 'package:newarc_platform/pages/agency/terms_and_conditions.dart';
import 'package:newarc_platform/pages/404_page.dart';
import 'package:newarc_platform/pages/agency/home_agency.dart';
import 'package:newarc_platform/pages/agency/models/home_work.model.dart';
import 'package:newarc_platform/pages/home_scouter.dart';
import 'package:newarc_platform/pages/login_page.dart';
import 'package:newarc_platform/pages/models/home_scouter.model.dart';
import 'package:newarc_platform/pages/successful_payment_page.dart';
import 'package:newarc_platform/pages/register_page.dart';
import 'package:newarc_platform/pages/user_management.dart';
import 'package:newarc_platform/pages/work/home_newarc.dart';
import 'package:newarc_platform/widget/work/project/add_property_form.dart';
import 'package:newarc_platform/pages/supplier/home_supplier.dart';
import 'package:newarc_platform/pages/supplier/models/home_supplier.model.dart';
import 'package:newarc_platform/pages/professionals/home_professionals.dart';
import 'package:newarc_platform/pages/professionals/models/home_professionals.model.dart';

class RoutesName {
  // ignore: non_constant_identifier_names
  static const String USER_MANAGEMENT = '/usermgmt';
  static const String PAGE404 = '/404';
  // ignore: non_constant_identifier_names
  // static const String SECOND_PAGE = '/second_page';
}

class RouteGenerator {
  static Route<dynamic>? generateRoute(RouteSettings settings) {
    final args = settings.arguments;
    print('settings.name: ${settings.name}');
    if (settings.name == LoginPage.route) {
      return MaterialPageRoute(
          builder: (_) => LoginPage(
                loginType: args != null ? args as String : 'agency',
              ));
    } else if (settings.name == RegisterPage.route) {
      return MaterialPageRoute(builder: (_) => RegisterPage(
        origin: args != null ? args as String : 'agency',
      ));
    } else if (settings.name == AddPropertyForm.route) {
      return MaterialPageRoute(builder: (_) => AddPropertyForm());
      /*} else if (settings.name ==  PropertyIndex.route ) {
      return MaterialPageRoute(builder: (_) => PropertyIndex());*/
    } else if (settings.name == HomeProfessionals.route) {
      if (args is HomeProfessionalsArguments) {
        return MaterialPageRoute(
            builder: (_) => HomeProfessionals(
                  professionalsUser: args.professionalsUser!,
                ),
            settings: settings);
      } else {
        errorRoute();
      }
    } else if (settings.name == HomeSupplier.route) {
      if (args is HomeSupplierArguments) {
        return MaterialPageRoute(
            builder: (_) => HomeSupplier(
                  supplierUser: args.supplierUser!,
                ),
            settings: settings);
      } else {
        errorRoute();
      }
    } else if (settings.name == HomeAgency.route) {
      if (args is HomeAgencyArguments) {
        return MaterialPageRoute(
            builder: (_) => HomeAgency(
                  agencyUser: args.agencyUser!,
                ),
            settings: settings);
      } else {
        errorRoute();
      }
    } else if (settings.name == HomeNewarc.route) {
      if (args is HomeNewarcArguments) {
        return MaterialPageRoute(
            builder: (_) => HomeNewarc(
                  newarcUser: args.agencyUser!,
                ),
            settings: settings);
      } else {
        errorRoute();
      }
    } else if (settings.name != "" &&
        settings.name!.contains(RoutesName.USER_MANAGEMENT)) {
      return MaterialPageRoute(
          builder: (_) => UserManagementPage(), settings: settings);
    } else if (settings.name == Page404.route) {
      return MaterialPageRoute(builder: (_) => Page404(), settings: settings);
    } else if (settings.name == HomeScouter.route) {
      return MaterialPageRoute(
          builder: (_) => HomeScouter(), settings: settings);
      // if (args is HomeScouterArguments) {
      //     return MaterialPageRoute(
      //       builder: (_) => HomeScouter(
      //           //currentUser: args.currentUser,
      //           ),
      //     );
      //   } else {
      //     errorRoute();
      //   }
    } else if (settings.name == AgencyTermsAndConditions.route) {
      print('T and c');
      return MaterialPageRoute(builder: (_) => AgencyTermsAndConditions());
    } else if (settings.name == SuccessfulPaymentPage.route) {
      return MaterialPageRoute(builder: (_) => SuccessfulPaymentPage());
    } else {
      return MaterialPageRoute(builder: (_) => Page404(), settings: settings);
    }

    // switch (settings.name) {
    //   case settings.name.contains('test'):
    //     return MaterialPageRoute(builder: (_) => UserManagementPage());

    //   case HomeScouter.route:
    //     return MaterialPageRoute(builder: (_) => HomeScouter());
    //     if (args is HomeScouterArguments) {
    //       return MaterialPageRoute(
    //         builder: (_) => HomeScouter(
    //             //currentUser: args.currentUser,
    //             ),
    //       );
    //     } else {
    //       errorRoute();
    //     }

    //     break;

    //   default:
    //     errorRoute();
    //     break;
    // }
  }
}

errorRoute() {
  return MaterialPageRoute(builder: (_) => Page404());
}
