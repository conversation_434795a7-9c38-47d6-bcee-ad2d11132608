import 'dart:developer';

import 'package:cloud_functions/cloud_functions.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';
import 'package:newarc_platform/classes/agencyUser.dart';
import 'package:url_launcher/url_launcher.dart';

const int MINIMUM_ROI = 15;
const int MAXIMUM_ROI = 30;

String formatPriceText(double price) {
  return (price / 1000).ceil().toString();
}

double computeMinimumOfferPrice(double predictedPrice) {
  double minimum_roi = predictedPrice * MINIMUM_ROI / 100;
  return predictedPrice - minimum_roi;
}

double computeMaximumOfferPrice(double predictedPrice) {
  double maximum_roi = predictedPrice * MAXIMUM_ROI / 100;
  return predictedPrice - maximum_roi;
}

Future<bool?> sendEmail(
    {required int templateId,
     required String subject,
     required String? recipientEmail,
     required String? recipientName,
    Map<String, dynamic>? variables}) async {
  try {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'sendNotificationEmail',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 5),
      ),
    );
    Map<String, dynamic> query = {
      "templateId": templateId,
      "sendingEmail": "<EMAIL>",
      "sendingName": "Newarc",
      "subject": subject,
      "recipientEmail": recipientEmail,
      "recipientName": recipientName,
      "variables": variables
    };
    log("send mail query $query");
    HttpsCallableResult result = await callable.call(query);
    log("send mail result ${result.data}");
    return true;
  } catch (e, s) {
    // print({'email', e.toString(), s});
    return false;
  }
}

Future<void> launchCustomUrl(String url) async {
  Uri _url = Uri.parse(url);

  if (!await launchUrl(_url)) {
    throw 'Could not launch $_url';
  }
}

//TODO the name of this method should be change, maybe something like handlefirebaseAuthLinks
Future<bool> verifyUserEmail(Map<String, dynamic> verificationParams) async {
  try {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'testFunction',
      //'verifyUserEmail',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 5),
      ),
    );
    HttpsCallableResult result = await callable.call(verificationParams);
    debugPrint(result.data.toString());
    return result.data;
  } catch (e) {
    debugPrint(e.toString());
    return false;
  }
}

Future<bool> sendVerificationLinkEmail(User user) async {
  try {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'sendVerificationLinkEmail',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 10),
      ),
    );

    Map<String, dynamic> query = {
      'email': user.email,
      'name': user.displayName ?? ''
    };
    HttpsCallableResult result = await callable.call(query);
    debugPrint(result.data);
    return result.data == "true" ? true : false;
  } catch (e) {
    debugPrint(e.toString());
    return false;
  }
}

getMailjetTemplateId() {
  if (false) {
    return 4288680;
  }
}
// static const Map<String, String> stripeAbbonamentoPaymentLinks = {
//   'start + success fee': "price_1QX193BCCTTeQsGGO8LMMTf6",
//   'premium + success fee': "price_1QX1CTBCCTTeQsGGad1lXcvk",
//   'gold + success fee': "price_1QX1DJBCCTTeQsGGtx2Hpf41",
//   'start': "price_1QX1FxBCCTTeQsGGlWXgjSaz",
//   'premium': "price_1QX1GzBCCTTeQsGG6xhKzg2O",
//   'gold': "price_1QX1HzBCCTTeQsGGW54bMIhB",
// };

Future<Map> getStripeCheckoutLink(List<String> stripePriceIds, String? immaginaProjectId, String userId, {String origin = "immagina", String userType = "agency"}) async{
  try {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'getStripeCheckoutLink',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 10),
      ),
    );
    Map query = {
      'priceIds': stripePriceIds,
      'origin': origin,
      'immaginaProjectId': immaginaProjectId,
      'userId': userId,
      'userType': userType,
    };
    HttpsCallableResult result = await callable.call(query);
    return result.data;
  } catch(e) {
    print(e.toString());
    return {};
  }
}

Future<Map> getStripeCheckoutLinkForAgencyReport({required List<String> stripePriceIds,required String userId,required String userType,String origin = "buyer_report"}) async{
  try {
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'getStripeCheckoutLinkForAgencyReport',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 10),
      ),
    );
    Map query = {
      'priceIds': stripePriceIds,
      'origin': origin,
      'userId': userId,
      'userType': userType,
    };
    HttpsCallableResult result = await callable.call(query);
    return result.data;
  } catch(e) {
    print(e.toString());
    return {};
  }
}


Future<bool> sendProjectJobModificationNotification(String projectId, String title, String message) async{
  try{
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'sendProjectJobModificationNotification',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 5),
      ),
    );
    Map query = {
      'projectId': projectId,
      'title': title,
      'message': message
    };
    HttpsCallableResult result = await callable.call(query);
    return result.data == "true" ? true : false;
  } catch(e) {
    print(e.toString());
    return false;
  }
}

Future<Map> getImmobiliareComparabili(
    {
    required String latitude,
    required String longitude,
    required String maintenanceStatus,
    int? gSF,
    int? rooms,
    int? bathrooms,
    int? radius,
    bool? rent,
    bool? residential}) async{
  try{
    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'getImmobiliareComparabili',
      options: HttpsCallableOptions(
        timeout: const Duration(minutes: 1),
      ),
    );
    Map query = {
      'latitude': latitude,
      'longitude': longitude,
      'maintenanceStatus': maintenanceStatus,
    };
    if(gSF != null) query['gSF'] = gSF;
    if(rooms != null) query['rooms'] = rooms;
    if(bathrooms != null) query['bathrooms'] = bathrooms;
    if(radius != null) query['radius'] = radius;
    if(rent != null) query['rent'] = rent;
    if(residential != null) query['residential'] = residential;

    HttpsCallableResult result = await callable.call(query);
    return result.data;
  }on FirebaseFunctionsException catch (e) {
    print("FirebaseFunctionsException: ${e.code} - ${e.message}");
    return {};
  } catch(e) {
    print(e.toString());
    return {};
  }
}

Future<Map> sendResetPasswordEmail(String email) async {
  HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
    'handlePasswordReset_piattaforma',
    options: HttpsCallableOptions(
      timeout: const Duration(seconds: 10),
    ),
  );
  Map query = {
    'email': email,
  };
  HttpsCallableResult result = await callable.call(query);
  return result.data;
}

Future<Map<String, int>> fetchNearbyPlaceCountsFromCloud({
  required String latitude,
  required String longitude,
  required int radius,
  required List<String> types,
}) async {
  try{
    Map queryParams = {
      'lat': latitude.toString(),
      'lng': longitude.toString(),
      'radius': radius.toString(),
      'types': types.join(','), // e.g. "school,park"
    };

    HttpsCallable callable = FirebaseFunctions.instance.httpsCallable(
      'fetchNearbyPlaces',
      options: HttpsCallableOptions(
        timeout: const Duration(seconds: 30),
      ),
    );


    HttpsCallableResult result = await callable.call(queryParams);
    if (result.data == null || result.data is! Map) {
      print('No data returned from Cloud Function');
      return {};
    }

    final data = result.data as Map<String, dynamic>;

    if (data['status'] != 200 || data['data'] == null) {
      print('Invalid status or results in response');
      return {};
    }

    final rawResults = data['data'] as Map<String, dynamic>;
    return rawResults.map((key, value) => MapEntry(key, value as int));
  }catch(e){
    print("Error while fetchNearbyPlaceCountsFromCloud ${e.toString()}");
    return {};
  }
}